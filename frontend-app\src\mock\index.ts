// 模拟数据
export const mockUsers = [
  {
    id: 1,
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'admin',
    avatar: '/images/avatar1.jpg'
  },
  {
    id: 2,
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'user',
    avatar: '/images/avatar2.jpg'
  }
]

export const mockTokens = [
  {
    id: 1,
    name: 'Development Token',
    value: 'dev_token_123',
    expiresAt: '2024-12-31',
    scopes: ['read', 'write']
  },
  {
    id: 2,
    name: 'Production Token',
    value: 'prod_token_456',
    expiresAt: '2024-06-30',
    scopes: ['read']
  }
]

export const mockSystemStatus = {
  status: 'healthy',
  uptime: '99.9%',
  lastCheck: new Date().toISOString(),
  services: {
    database: 'online',
    redis: 'online',
    api: 'online'
  }
}

// 模拟API响应延迟
export const delay = (ms: number = 1000) => 
  new Promise(resolve => setTimeout(resolve, ms))

// 模拟API调用
export const mockApi = {
  getUsers: async () => {
    await delay(500)
    return { data: mockUsers, success: true }
  },
  
  getTokens: async () => {
    await delay(800)
    return { data: mockTokens, success: true }
  },
  
  getSystemStatus: async () => {
    await delay(300)
    return { data: mockSystemStatus, success: true }
  }
} 
