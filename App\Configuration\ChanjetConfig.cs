﻿

namespace ChanjetOAuthTest.App.Configuration
{
    public class ChanjetConfig
    {
        public string MarketAuthorizeUrl { get; set; } // 替换旧的 AuthorityUrl
        public string TokenUrl { get; set; }
        public string ApiBaseUrl { get; set; }
        public string AppKey { get; set; }
        public string AppSecret { get; set; }
        public string RedirectUri { get; set; }
        public string Scope { get; set; }
        public string AppName { get; set; }
        public string TestOrgId { get; set; }
        
        /// <summary>
        /// 环境密钥 - 用于AES解密推送消息，16位字符串
        /// </summary>
        public string EnvironmentKey { get; set; }
    }
}
