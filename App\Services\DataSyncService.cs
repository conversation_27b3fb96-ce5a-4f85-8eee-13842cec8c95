using ChanjetOAuthTest.App.Common;
using ChanjetOAuthTest.App.Interfaces;
using ChanjetOAuthTest.App.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace ChanjetOAuthTest.App.Services
{
    /// <summary>
    /// 数据同步服务，处理API数据同步相关功能
    /// </summary>
    public class DataSyncService : IDataSyncService, IDisposable
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<DataSyncService> _logger;
        private readonly Dictionary<int, HttpClient> _httpClients = new Dictionary<int, HttpClient>();
        private readonly Dictionary<int, string> _encryptedSecrets = new Dictionary<int, string>();
        private readonly ConcurrentDictionary<int, bool> _syncStatus = new ConcurrentDictionary<int, bool>();
        private readonly List<CancellationTokenSource> _cancellationTokens = new List<CancellationTokenSource>();
        private List<ApiEndpoint> _apiEndpoints;
        private List<UserAccount> _userAccounts;
        private bool _disposed = false;

        public DataSyncService(IConfiguration configuration, ILogger<DataSyncService> logger)
        {
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            
            // 初始化日志目录
            LogHelper.InitializeLogDirectories();
            
            LoadConfiguration();
        }

        /// <summary>
        /// 加载配置
        /// </summary>
        private void LoadConfiguration()
        {
            try
            {
                _apiEndpoints = _configuration.GetSection("DataSync:ApiEndpoints").Get<List<ApiEndpoint>>() ?? new List<ApiEndpoint>();
                _userAccounts = _configuration.GetSection("DataSync:UserAccounts").Get<List<UserAccount>>() ?? new List<UserAccount>();

                _logger.LogInformation($"加载配置完成：API端点 {_apiEndpoints.Count} 个，用户账户 {_userAccounts.Count} 个");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载配置失败");
            }
        }

        /// <summary>
        /// 启动API同步任务
        /// </summary>
        public void StartApiSync()
        {
            try
            {
                if (_userAccounts == null || _userAccounts.Count == 0)
                {
                    LogHelper.LogApiMessage("错误: 未找到用户账户配置");
                    return;
                }

                // 初始化HTTP客户端和加密信息
                for (int i = 0; i < _userAccounts.Count; i++)
                {
                    var account = _userAccounts[i];
                    _httpClients[i] = new HttpClient();

                    var loginInfo = new Dictionary<string, string>
                    {
                        { "FUSERNAME", account.Username },
                        { "FPASSWORD", account.Password },
                        { "CompanyId", account.CompanyId }
                    };

                    try
                    {
                        _encryptedSecrets[i] = RSAHelper.RSAEncryption(
                            JsonConvert.SerializeObject(loginInfo),
                            account.PublicKey
                        );
                    }
                    catch (Exception ex)
                    {
                        LogHelper.LogApiMessage($"账户 {account.CompanyName} RSA加密失败: {ex.Message}");
                        continue;
                    }

                    // 启动同步任务
                    int accountIndex = i; // 捕获循环变量
                    var cancellationTokenSource = new CancellationTokenSource();
                    _cancellationTokens.Add(cancellationTokenSource);
                    
                    Task.Run(() => RunDataSync(accountIndex, cancellationTokenSource.Token));
                    _syncStatus[accountIndex] = true;
                }

                LogHelper.LogApiMessage("API同步任务已启动");
            }
            catch (Exception ex)
            {
                LogHelper.LogApiMessage($"API同步任务启动失败: {ex.Message}");
                _logger.LogError(ex, "API同步任务启动失败");
            }
        }

        /// <summary>
        /// 停止API同步任务
        /// </summary>
        public void StopApiSync()
        {
            try
            {
                foreach (var cancellationToken in _cancellationTokens)
                {
                    cancellationToken.Cancel();
                }
                
                _cancellationTokens.Clear();
                _syncStatus.Clear();
                
                LogHelper.LogApiMessage("API同步任务已停止");
            }
            catch (Exception ex)
            {
                LogHelper.LogApiMessage($"停止API同步任务失败: {ex.Message}");
                _logger.LogError(ex, "停止API同步任务失败");
            }
        }

        /// <summary>
        /// 运行数据同步
        /// </summary>
        /// <param name="accountIndex">账户索引</param>
        /// <param name="cancellationToken">取消令牌</param>
        private async Task RunDataSync(int accountIndex, CancellationToken cancellationToken)
        {
            string companyName = "未知公司";
            try
            {
                var apiSettings = _configuration.GetSection("DataSync:ApiSettings");
                string baseUrl = apiSettings.GetValue<string>("BaseUrl");
                string loginEndpoint = apiSettings.GetValue<string>("LoginEndpoint");
                int syncInterval = apiSettings.GetValue<int>("MainSyncInterval");

                var httpClient = _httpClients[accountIndex];
                string encryptedSecret = _encryptedSecrets[accountIndex];
                
                if (accountIndex < _userAccounts.Count)
                {
                    companyName = _userAccounts[accountIndex].CompanyName ?? "未知公司";
                }

                // 首次登录获取Token
                await SetAuthToken(accountIndex, baseUrl, loginEndpoint, encryptedSecret, companyName);

                // 循环执行同步操作
                while (!cancellationToken.IsCancellationRequested)
                {
                    try
                    {
                        LogHelper.LogApiMessage($"[{companyName}] 开始同步 - {DateTime.Now}");
                        
                        // 验证token是否有效
                        bool isValid = await ValidateTokenAsync(httpClient, baseUrl);
                        if (!isValid)
                        {
                            LogHelper.LogApiMessage($"[{companyName}] Token无效，重新获取");
                            await SetAuthToken(accountIndex, baseUrl, loginEndpoint, encryptedSecret, companyName);
                        }
                        
                        // 执行所有已配置的接口
                        foreach (var endpoint in _apiEndpoints.Where(e => e.Enabled))
                        {
                            if (cancellationToken.IsCancellationRequested) break;
                            
                            await ExecuteApiEndpoint(httpClient, baseUrl, endpoint, companyName);
                        }
                    }
                    catch (Exception ex)
                    {
                        LogHelper.LogApiMessage($"[{companyName}] 同步过程出错: {ex.Message}");
                        
                        // 尝试重新登录
                        try
                        {
                            await SetAuthToken(accountIndex, baseUrl, loginEndpoint, encryptedSecret, companyName);
                        }
                        catch (Exception loginEx)
                        {
                            LogHelper.LogApiMessage($"[{companyName}] 重新登录失败: {loginEx.Message}");
                        }
                    }
                    
                    // 等待下一次同步
                    await Task.Delay(syncInterval, cancellationToken);
                }
            }
            catch (OperationCanceledException)
            {
                LogHelper.LogApiMessage($"[{companyName}] 同步任务已取消");
            }
            catch (Exception ex)
            {
                LogHelper.LogApiMessage($"[{companyName}] 任务执行失败: {ex.Message}");
                _logger.LogError(ex, $"[{companyName}] 任务执行失败");
            }
            finally
            {
                _syncStatus.TryRemove(accountIndex, out _);
            }
        }

        /// <summary>
        /// 执行API接口
        /// </summary>
        private async Task<string> ExecuteApiEndpoint(HttpClient httpClient, string baseUrl, ApiEndpoint endpoint, string companyName)
        {
            try
            {
                LogHelper.LogApiMessage($"[{companyName}] 执行接口: {endpoint.Name}");
                var content = new StringContent(endpoint.RequestBodyTemplate, Encoding.UTF8, "application/json");
                
                HttpResponseMessage response;
                if (endpoint.Method.ToUpper() == "GET")
                {
                    response = await httpClient.GetAsync($"{baseUrl}{endpoint.Path}");
                }
                else
                {
                    response = await httpClient.PostAsync($"{baseUrl}{endpoint.Path}", content);
                }
                
                string responseContent = await response.Content.ReadAsStringAsync();
                LogHelper.LogApiMessage($"[{companyName}] {endpoint.Name} 结果: {responseContent}");
                
                // 检查Token是否有效
                if (responseContent.Contains("\"StatusCode\":\"400\""))
                {
                    LogHelper.LogApiMessage($"[{companyName}] Token可能已失效，后续将尝试重新获取");
                }
                
                return responseContent;
            }
            catch (Exception ex)
            {
                LogHelper.LogApiMessage($"[{companyName}] 执行接口 {endpoint.Name} 失败: {ex.Message}");
                return $"{{\"Error\":\"{ex.Message}\"}}";
            }
        }

        /// <summary>
        /// 设置认证Token
        /// </summary>
        private async Task SetAuthToken(int accountIndex, string baseUrl, string loginEndpoint, string encryptedSecret, string companyName)
        {
            try
            {
                var httpClient = _httpClients[accountIndex];
                var content = new StringContent($"{{\"encryptedSecret\":\"{encryptedSecret}\"}}", Encoding.UTF8, "application/json");
                
                var response = await httpClient.PostAsync($"{baseUrl}{loginEndpoint}", content);
                var responseContent = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    dynamic result = JsonConvert.DeserializeObject(responseContent);
                    if (result?.token != null)
                    {
                        httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", result.token.ToString());
                        LogHelper.LogApiMessage($"[{companyName}] Token设置成功");
                    }
                }
                else
                {
                    LogHelper.LogApiMessage($"[{companyName}] 登录失败: {responseContent}");
                }
            }
            catch (Exception ex)
            {
                LogHelper.LogApiMessage($"[{companyName}] 设置Token失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 验证Token是否有效
        /// </summary>
        public async Task<bool> ValidateTokenAsync(int accountIndex)
        {
            if (!_httpClients.ContainsKey(accountIndex))
                return false;
                
            var apiSettings = _configuration.GetSection("DataSync:ApiSettings");
            string baseUrl = apiSettings.GetValue<string>("BaseUrl");
            
            return await ValidateTokenAsync(_httpClients[accountIndex], baseUrl);
        }

        /// <summary>
        /// 验证Token是否有效（内部方法）
        /// </summary>
        private async Task<bool> ValidateTokenAsync(HttpClient httpClient, string baseUrl)
        {
            try
            {
                var apiSettings = _configuration.GetSection("DataSync:ApiSettings");
                string validateEndpoint = apiSettings.GetValue<string>("ValidateTokenEndpoint");
                
                var response = await httpClient.GetAsync($"{baseUrl}{validateEndpoint}");
                var responseContent = await response.Content.ReadAsStringAsync();
                
                return response.IsSuccessStatusCode && !responseContent.Contains("\"StatusCode\":\"400\"");
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 执行自定义API调用
        /// </summary>
        public async Task<string> ExecuteCustomApiCall(int accountIndex, string endpoint, string requestBody = "{}")
        {
            try
            {
                if (!_httpClients.ContainsKey(accountIndex))
                    return "{\"Error\":\"无效的账户索引\"}";

                var apiSettings = _configuration.GetSection("DataSync:ApiSettings");
                string baseUrl = apiSettings.GetValue<string>("BaseUrl");
                var httpClient = _httpClients[accountIndex];
                
                var content = new StringContent(requestBody, Encoding.UTF8, "application/json");
                var response = await httpClient.PostAsync($"{baseUrl}{endpoint}", content);
                
                return await response.Content.ReadAsStringAsync();
            }
            catch (Exception ex)
            {
                return $"{{\"Error\":\"{ex.Message}\"}}";
            }
        }

        /// <summary>
        /// 刷新API端点配置
        /// </summary>
        public void RefreshApiEndpoints()
        {
            try
            {
                LoadConfiguration();
                LogHelper.LogApiMessage("API端点配置已刷新");
            }
            catch (Exception ex)
            {
                LogHelper.LogApiMessage($"刷新API端点配置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取同步状态
        /// </summary>
        public Dictionary<string, object> GetSyncStatus()
        {
            var status = new Dictionary<string, object>
            {
                ["总账户数"] = _userAccounts?.Count ?? 0,
                ["运行中任务数"] = _syncStatus.Count,
                ["启动时间"] = DateTime.Now,
                ["账户状态"] = new Dictionary<string, object>()
            };

            if (_userAccounts != null)
            {
                var accountStatus = (Dictionary<string, object>)status["账户状态"];
                for (int i = 0; i < _userAccounts.Count; i++)
                {
                    var account = _userAccounts[i];
                    accountStatus[account.CompanyName] = new
                    {
                        运行状态 = _syncStatus.ContainsKey(i) ? "运行中" : "已停止",
                        账户索引 = i,
                        用户名 = account.Username,
                        公司ID = account.CompanyId
                    };
                }
            }

            return status;
        }

        /// <summary>
        /// 获取API端点列表
        /// </summary>
        public List<ApiEndpoint> GetApiEndpoints()
        {
            return _apiEndpoints?.ToList() ?? new List<ApiEndpoint>();
        }

        /// <summary>
        /// 更新API端点配置
        /// </summary>
        public bool UpdateApiEndpoints(List<ApiEndpoint> endpoints)
        {
            try
            {
                _apiEndpoints = endpoints?.ToList() ?? new List<ApiEndpoint>();
                LogHelper.LogApiMessage($"API端点配置已更新，共 {_apiEndpoints.Count} 个端点");
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.LogApiMessage($"更新API端点配置失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                StopApiSync();
                
                foreach (var httpClient in _httpClients.Values)
                {
                    httpClient?.Dispose();
                }
                
                _httpClients.Clear();
                _disposed = true;
            }
        }
    }
} 