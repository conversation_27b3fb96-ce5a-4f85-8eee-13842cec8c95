@echo off
setlocal enabledelayedexpansion

REM 畅捷通OAuth Token管理面板部署脚本 (Windows版)
REM 作者: Your Name
REM 版本: 1.0

title 畅捷通OAuth Token管理面板部署

echo.
echo =====================================
echo   畅捷通OAuth Token管理面板部署
echo =====================================
echo.

REM 检查.NET环境
:CHECK_DOTNET
echo [INFO] 检查.NET环境...
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] .NET SDK/Runtime 未安装。请先安装 .NET 5.0
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('dotnet --version') do set DOTNET_VERSION=%%i
echo [INFO] 当前.NET版本: !DOTNET_VERSION!

REM 检查Docker环境
:CHECK_DOCKER
echo [INFO] 检查Docker环境...
docker --version >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Docker未安装，跳过容器化部署选项
    set DOCKER_AVAILABLE=false
    goto MENU
)

docker info >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Docker未运行，跳过容器化部署选项
    set DOCKER_AVAILABLE=false
    goto MENU
)

echo [INFO] Docker环境正常
set DOCKER_AVAILABLE=true

REM 主菜单
:MENU
echo.
echo ==================================
echo   畅捷通OAuth Token管理面板部署
echo ==================================
echo 1. 传统部署 (直接发布)
echo 2. Docker部署 (单容器)
echo 3. Docker Compose部署 (推荐)
echo 4. 检查环境
echo 5. 退出
echo ==================================
echo.

set /p choice="请选择部署方式 [1-5]: "

if "%choice%"=="1" goto DEPLOY_TRADITIONAL
if "%choice%"=="2" goto DEPLOY_DOCKER
if "%choice%"=="3" goto DEPLOY_DOCKER_COMPOSE
if "%choice%"=="4" goto CHECK_ENV
if "%choice%"=="5" goto EXIT
echo [ERROR] 无效选择，请重新输入！
goto MENU

REM 传统部署
:DEPLOY_TRADITIONAL
echo [INFO] 开始传统部署...

REM 清理之前的发布
if exist "publish" (
    echo [INFO] 清理之前的发布文件...
    rmdir /s /q publish
)

REM 发布项目
echo [INFO] 发布项目...
dotnet publish -c Release -o ./publish

if errorlevel 1 (
    echo [ERROR] 项目发布失败！
    pause
    exit /b 1
)

echo [INFO] 项目发布成功！
echo [INFO] 发布目录: %CD%\publish
echo [INFO] 启动命令: cd publish ^&^& dotnet ChanjetOAuthTest.dll
echo [INFO] 访问地址: http://localhost:35010/page/dashboard.html
echo.
echo 是否立即启动应用？ (y/n)
set /p start_app=""
if /i "%start_app%"=="y" (
    echo [INFO] 启动应用...
    cd publish
    start "畅捷通OAuth面板" dotnet ChanjetOAuthTest.dll
    cd ..
    echo [INFO] 应用已启动，请访问: http://localhost:35010/page/dashboard.html
)
goto END

REM Docker部署
:DEPLOY_DOCKER
if "%DOCKER_AVAILABLE%"=="false" (
    echo [ERROR] Docker环境不可用！
    goto MENU
)

echo [INFO] 开始Docker部署...

REM 构建镜像
echo [INFO] 构建Docker镜像...
docker build -t chanjet-oauth-panel:latest .

if errorlevel 1 (
    echo [ERROR] Docker镜像构建失败！
    pause
    exit /b 1
)

echo [INFO] Docker镜像构建成功！

REM 停止现有容器
docker ps -q -f name=chanjet-oauth >nul 2>&1
if not errorlevel 1 (
    echo [INFO] 停止现有容器...
    docker stop chanjet-oauth
    docker rm chanjet-oauth
)

REM 启动新容器
echo [INFO] 启动新容器...
docker run -d --name chanjet-oauth -p 35010:35010 --restart unless-stopped chanjet-oauth-panel:latest

if errorlevel 1 (
    echo [ERROR] 容器启动失败！
    pause
    exit /b 1
)

echo [INFO] 容器启动成功！
echo [INFO] 访问地址: http://localhost:35010/page/dashboard.html
echo [INFO] 查看日志: docker logs chanjet-oauth
goto END

REM Docker Compose部署
:DEPLOY_DOCKER_COMPOSE
if "%DOCKER_AVAILABLE%"=="false" (
    echo [ERROR] Docker环境不可用！
    goto MENU
)

echo [INFO] 开始Docker Compose部署...

if not exist "docker-compose.yml" (
    echo [ERROR] docker-compose.yml 文件不存在！
    pause
    exit /b 1
)

REM 停止现有服务
echo [INFO] 停止现有服务...
docker-compose down

REM 构建并启动服务
echo [INFO] 构建并启动服务...
docker-compose up -d --build

if errorlevel 1 (
    echo [ERROR] Docker Compose部署失败！
    pause
    exit /b 1
)

echo [INFO] Docker Compose部署成功！
echo [INFO] 访问地址: http://localhost:35010/page/dashboard.html
echo [INFO] 查看日志: docker-compose logs -f
echo [INFO] 查看状态: docker-compose ps
goto END

REM 检查环境
:CHECK_ENV
goto CHECK_DOTNET

REM 退出
:EXIT
echo [INFO] 退出部署脚本
exit /b 0

REM 结束
:END
echo.
echo 部署完成！
pause 