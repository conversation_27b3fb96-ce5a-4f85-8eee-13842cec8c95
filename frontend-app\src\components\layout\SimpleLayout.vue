<script setup lang="ts">
import BaseLayout from './BaseLayout.vue'

interface Props {
  title?: string
  showBack?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  showBack: false
})
</script>

<template>
  <BaseLayout
    :show-sidebar="false"
    main-class="container mx-auto px-6 py-8 max-w-4xl"
    header-class="sticky top-0 z-40 flex h-16 items-center bg-white/95 backdrop-blur-xl border-b border-slate-200/60 shadow-sm px-6"
  >
    <template #header>
      <div class="flex items-center space-x-4">
        <button 
          v-if="showBack" 
          @click="$router.back()" 
          class="flex items-center text-slate-600 hover:text-slate-800 transition-colors"
        >
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
          </svg>
          返回
        </button>
        
        <h1 v-if="title" class="text-xl font-semibold text-slate-800">{{ title }}</h1>
      </div>
    </template>

    <slot />
  </BaseLayout>
</template> 
