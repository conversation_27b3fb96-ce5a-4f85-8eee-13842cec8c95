{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "AllowedHosts": "*", "Kestrel": {"Endpoints": {"Http": {"Url": "http://0.0.0.0:35010"}}}, "Chanjet": {"MarketAuthorizeUrl": "https://market.chanjet.com/user/v2/authorize", "TokenUrl": "https://openapi.chanjet.com/auth/v2/getToken", "ApiBaseUrl": "https://api.chanjet.com/tplus/api/v2", "AppKey": "SEsSOV9k", "AppSecret": "99A800B07E74CDFCAC8CC51C7C7A68A4", "AppName": "tpluscloud", "TestOrgId": "1240313174767478", "RedirectUri": "http://***********:35010/oauth/callback", "Scope": "auth_all", "EnvironmentKey": "AEUYxGYKvnIAIfPK"}, "PermanentAuth": {"UserPermanentCodes": {}, "OrgPermanentCodes": {}, "AppTicket": {"Value": "", "LastUpdated": "", "ExpiresAt": ""}, "OrgAccessTokens": {}}, "DataSync": {"ApiSettings": {"BaseUrl": "http://*************:1280", "LoginEndpoint": "/api/ClientUser/LoginAsync", "ValidateTokenEndpoint": "/api/ClientUser/ValidateTokenAsync", "MainSyncInterval": 180000, "KeepAliveInterval": 1200000}, "ApiEndpoints": [{"Name": "同步T+数据", "Path": "/api/SYN001SyncTPlusData/SyncTPlus", "Method": "POST", "RequestBodyTemplate": "{}", "Description": "同步T+数据到系统", "Enabled": true}, {"Name": "拉取T+数据", "Path": "/api/SYN001SyncTPlusData/PullDataFromTPlusAsync", "Method": "POST", "RequestBodyTemplate": "{\"para\":{}}", "Description": "从T+拉取数据", "Enabled": true}, {"Name": "清除缓存", "Path": "/api/MSD002Material/ClearCatchAsync", "Method": "POST", "RequestBodyTemplate": "{}", "Description": "清除系统缓存", "Enabled": true}], "FileSync": {"SourceDirectory": "D:\\Chanjet\\TPlusStd\\WebSite\\UserImages", "TargetDirectory": "D:\\minio\\hcloud\\MSD002MaterialEdit", "SyncInterval": 180000}, "UserAccounts": [{"CompanyName": "迪艾斯测试账套", "Username": "***********", "Password": "a123456", "CompanyId": "0327", "PublicKey": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAg/NtnPV0/rA0+oz6qw45o8K4GJg6Y0GLrffQW+4HXnKLtIbno3iFgJgmdAeatu7d4WrkJDztec2cChVjLfqTPsHKWVgTkeoe4zm0X5T3d9y1ngJmpwgNoWjmddNU6GjVTarXyEG3qcqhihYhnBeoiFxES7GKgQVeRSQfKAztDXKkDwcDQttB9w7qqVUrioqU3gbEndZmKLMRB5S1XquP+FFP5LL+7HeC2eSjZJ/+zDIia2A0m0KMhNB26WkT7Tq4PejofNvVltpvAbfVOdADOypZ9MSXMJ/qRdRM5s86YoWfrYAA84BLEmTJJoMrgIh/HmliVmRTP+pTWYSg4kFGkwIDAQAB"}]}, "ExternalTokenService": {"ClientCredentials": [{"ClientId": "<PERSON><PERSON><PERSON>", "ClientName": "迪艾斯", "ClientSecret": "P1NhdjSjyPKq"}, {"ClientId": "test", "ClientName": "测试用户", "ClientSecret": "TestSecret123"}, {"ClientId": "admin", "ClientName": "管理员", "ClientSecret": "AdminSecret456"}]}, "ConnectionStrings": {"DefaultConnection": "User ID=MES;Password=**********;Initial Catalog=ZY_MAIN;Server=.;TrustServerCertificate=True"}}