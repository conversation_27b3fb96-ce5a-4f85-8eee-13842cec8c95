# .dockerignore文件
# 用于优化Docker构建，排除不必要的文件

# 构建输出
bin/
obj/
publish/
out/

# IDE文件
.vs/
.vscode/
*.user
*.suo
*.userosscache
*.sln.docstates

# 调试文件
*.pdb

# 包管理器缓存
packages/
node_modules/

# 日志文件
logs/
*.log

# 临时文件
tmp/
temp/
*.tmp
*.temp

# Git
.git/
.gitignore
README.md
*.md

# Docker相关
Dockerfile
.dockerignore
docker-compose*.yml

# 部署脚本
deploy.sh
deploy.bat

# 数据文件
data/

# OS生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 环境变量文件
.env
.env.local
.env.development
.env.production 