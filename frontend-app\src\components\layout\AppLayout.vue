<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { Loader2 } from 'lucide-vue-next'

// 配置和工具函数
import { getNavigationItemByPath } from '@/config/navigation'
import { useSystemStatus } from '@/composables/useSystemStatus'

// Sidebar 组件
import { SidebarProvider, SidebarInset } from '@/components/ui/sidebar'

// 子组件
import AppSidebar from './AppSidebar.vue'
import AppHeader from './AppHeader.vue'

const route = useRoute()

// 系统状态管理
const {
  systemStatus,
  isLoading: isStatusLoading,
  lastUpdated,
  refreshSystemStatus,
  getStatusColor
} = useSystemStatus()

// 当前页面信息
const currentPageInfo = computed(() => {
  const currentItem = getNavigationItemByPath(route.path)
  return currentItem || { name: '管理面板', href: '/' }
})

// 面包屑导航数据
const breadcrumbItems = computed(() => {
  const items = []
  
  // 根页面
  if (route.path !== '/') {
    items.push({ name: '管理面板', href: '/' })
  }
  
  // 当前页面
  const currentItem = getNavigationItemByPath(route.path)
  if (currentItem && route.path !== '/') {
    items.push({ name: currentItem.title, href: route.path })
  }
  
  return items
})

// 通知数量
const notificationCount = computed(() => {
  return 3
})

// 系统状态项列表
const statusItems = computed(() => [
  { key: 'oauth', ...systemStatus.value.oauth },
  { key: 'token', ...systemStatus.value.token },
  { key: 'sync', ...systemStatus.value.sync }
])

// 格式化最后更新时间
const formattedLastUpdated = computed(() => {
  if (!lastUpdated.value) return ''
  
  const now = new Date()
  const diff = now.getTime() - lastUpdated.value.getTime()
  const minutes = Math.floor(diff / 60000)
  
  if (minutes < 1) return '刚刚更新'
  if (minutes < 60) return `${minutes}分钟前更新`
  
  const hours = Math.floor(minutes / 60)
  if (hours < 24) return `${hours}小时前更新`
  
  return lastUpdated.value.toLocaleDateString()
})
</script>

<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20">
    <SidebarProvider class="flex min-h-screen">
      <!-- 侧边栏 -->  
      <AppSidebar />

      <!-- 主内容区域 -->
      <SidebarInset class="flex flex-1 flex-col overflow-hidden">
        <!-- 顶部导航栏 -->
        <AppHeader
          :current-page-info="currentPageInfo"
          :breadcrumb-items="breadcrumbItems"
          :notification-count="notificationCount"
          :status-items="statusItems"
          :formatted-last-updated="formattedLastUpdated"
          :is-status-loading="isStatusLoading"
          :get-status-color="getStatusColor"
          @refresh-status="refreshSystemStatus"
        />

        <!-- 主要内容区域 -->
        <main class="flex-1 overflow-auto">
          <div class="container mx-auto px-6 py-8 max-w-7xl">
            <!-- 页面加载状态 -->
            <Suspense>
              <slot />
              <template #fallback>
                <div class="flex items-center justify-center py-12">
                  <div class="flex items-center space-x-3 text-slate-500">
                    <Loader2 class="h-5 w-5 animate-spin" />
                    <span>加载中...</span>
                  </div>
                </div>
              </template>
            </Suspense>
          </div>
        </main>
      </SidebarInset>
    </SidebarProvider>
  </div>
</template>

<style scoped>
/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 4px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.3);
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.5);
}

/* 状态指示器脉冲动画 */
@keyframes status-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.status-pulse {
  animation: status-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 平滑过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 响应式优化 */
@media (max-width: 640px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

/* 高对比度和可访问性 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
</style> 
