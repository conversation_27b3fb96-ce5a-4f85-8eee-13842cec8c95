// 用户相关类型
export interface User {
  id: number
  name: string
  email: string
  username: string
}

// 文章相关类型
export interface Post {
  id: number
  title: string
  body: string
  userId: number
}

// API 响应类型
export interface ApiResponse<T = any> {
  data: T
  message?: string
  status: number
}

// 通用分页类型
export interface Pagination {
  page: number
  pageSize: number
  total: number
}

// 列表响应类型
export interface ListResponse<T> {
  data: T[]
  pagination: Pagination
}

// 错误类型
export interface ApiError {
  message: string
  code: number
  details?: any
} 
