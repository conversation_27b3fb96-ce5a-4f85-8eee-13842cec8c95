#!/bin/bash

# 畅捷通OAuth Token管理面板部署脚本
# 作者: Your Name
# 版本: 1.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 函数: 打印信息
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查.NET环境
check_dotnet() {
    print_info "检查.NET环境..."
    if ! command -v dotnet &> /dev/null; then
        print_error ".NET SDK/Runtime 未安装。请先安装 .NET 5.0"
        exit 1
    fi
    
    DOTNET_VERSION=$(dotnet --version)
    print_info "当前.NET版本: $DOTNET_VERSION"
}

# 检查Docker环境
check_docker() {
    print_info "检查Docker环境..."
    if ! command -v docker &> /dev/null; then
        print_warning "Docker未安装，跳过容器化部署选项"
        return 1
    fi
    
    if ! docker info &> /dev/null; then
        print_warning "Docker未运行，跳过容器化部署选项"
        return 1
    fi
    
    print_info "Docker环境正常"
    return 0
}

# 传统部署
deploy_traditional() {
    print_info "开始传统部署..."
    
    # 清理之前的发布
    if [ -d "./publish" ]; then
        print_info "清理之前的发布文件..."
        rm -rf ./publish
    fi
    
    # 发布项目
    print_info "发布项目..."
    dotnet publish -c Release -o ./publish
    
    if [ $? -eq 0 ]; then
        print_info "项目发布成功！"
        print_info "发布目录: $(pwd)/publish"
        print_info "启动命令: cd publish && dotnet ChanjetOAuthTest.dll"
        print_info "访问地址: http://localhost:35010/page/dashboard.html"
    else
        print_error "项目发布失败！"
        exit 1
    fi
}

# Docker部署
deploy_docker() {
    print_info "开始Docker部署..."
    
    # 构建镜像
    print_info "构建Docker镜像..."
    docker build -t chanjet-oauth-panel:latest .
    
    if [ $? -eq 0 ]; then
        print_info "Docker镜像构建成功！"
        
        # 停止现有容器
        if [ "$(docker ps -q -f name=chanjet-oauth)" ]; then
            print_info "停止现有容器..."
            docker stop chanjet-oauth
            docker rm chanjet-oauth
        fi
        
        # 启动新容器
        print_info "启动新容器..."
        docker run -d \
            --name chanjet-oauth \
            -p 35010:35010 \
            --restart unless-stopped \
            chanjet-oauth-panel:latest
        
        if [ $? -eq 0 ]; then
            print_info "容器启动成功！"
            print_info "访问地址: http://localhost:35010/page/dashboard.html"
            print_info "查看日志: docker logs chanjet-oauth"
        else
            print_error "容器启动失败！"
            exit 1
        fi
    else
        print_error "Docker镜像构建失败！"
        exit 1
    fi
}

# Docker Compose部署
deploy_docker_compose() {
    print_info "开始Docker Compose部署..."
    
    if [ ! -f "docker-compose.yml" ]; then
        print_error "docker-compose.yml 文件不存在！"
        exit 1
    fi
    
    # 停止现有服务
    print_info "停止现有服务..."
    docker-compose down
    
    # 构建并启动服务
    print_info "构建并启动服务..."
    docker-compose up -d --build
    
    if [ $? -eq 0 ]; then
        print_info "Docker Compose部署成功！"
        print_info "访问地址: http://localhost:35010/page/dashboard.html"
        print_info "查看日志: docker-compose logs -f"
        print_info "查看状态: docker-compose ps"
    else
        print_error "Docker Compose部署失败！"
        exit 1
    fi
}

# 主菜单
show_menu() {
    echo ""
    echo "=================================="
    echo "  畅捷通OAuth Token管理面板部署"
    echo "=================================="
    echo "1. 传统部署 (直接发布)"
    echo "2. Docker部署 (单容器)"
    echo "3. Docker Compose部署 (推荐)"
    echo "4. 检查环境"
    echo "5. 退出"
    echo "=================================="
}

# 主程序
main() {
    print_info "畅捷通OAuth Token管理面板部署脚本"
    print_info "开始环境检查..."
    
    # 基本环境检查
    check_dotnet
    DOCKER_AVAILABLE=$(check_docker && echo "true" || echo "false")
    
    while true; do
        show_menu
        read -p "请选择部署方式 [1-5]: " choice
        
        case $choice in
            1)
                deploy_traditional
                break
                ;;
            2)
                if [ "$DOCKER_AVAILABLE" = "true" ]; then
                    deploy_docker
                    break
                else
                    print_error "Docker环境不可用！"
                fi
                ;;
            3)
                if [ "$DOCKER_AVAILABLE" = "true" ]; then
                    deploy_docker_compose
                    break
                else
                    print_error "Docker环境不可用！"
                fi
                ;;
            4)
                check_dotnet
                check_docker
                ;;
            5)
                print_info "退出部署脚本"
                exit 0
                ;;
            *)
                print_error "无效选择，请重新输入！"
                ;;
        esac
    done
}

# 执行主程序
main 