{"format": 1, "restore": {"F:\\modules\\开发\\ChanjetOAuthTest\\ChanjetOAuthTest.csproj": {}}, "projects": {"F:\\modules\\开发\\ChanjetOAuthTest\\ChanjetOAuthTest.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\modules\\开发\\ChanjetOAuthTest\\ChanjetOAuthTest.csproj", "projectName": "ChanjetOAuthTest", "projectPath": "F:\\modules\\开发\\ChanjetOAuthTest\\ChanjetOAuthTest.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\modules\\开发\\ChanjetOAuthTest\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net5.0"], "sources": {"http://mes:10881/repository/MESCore/": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "dependencies": {"Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}}}