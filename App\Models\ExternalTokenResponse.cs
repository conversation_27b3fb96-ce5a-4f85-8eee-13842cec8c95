using System;
using System.Text.Json.Serialization;

namespace ChanjetOAuthTest.App.Models
{
    /// <summary>
    /// 外部Token响应基类
    /// </summary>
    public class ExternalTokenResponse
    {
        /// <summary>
        /// 响应是否成功
        /// </summary>
        [JsonPropertyName("success")]
        public bool Success { get; set; }

        /// <summary>
        /// 错误代码
        /// </summary>
        [JsonPropertyName("error_code")]
        public string ErrorCode { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        [JsonPropertyName("error_message")]
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 响应时间戳
        /// </summary>
        [JsonPropertyName("timestamp")]
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 请求追踪ID
        /// </summary>
        [JsonPropertyName("trace_id")]
        public string TraceId { get; set; }
    }

    /// <summary>
    /// 外部Token获取响应
    /// </summary>
    public class ExternalTokenAcquireResponse : ExternalTokenResponse
    {
        /// <summary>
        /// Token信息
        /// </summary>
        [JsonPropertyName("token")]
        public TokenInfo Token { get; set; }

        /// <summary>
        /// 应用密钥
        /// </summary>
        [JsonPropertyName("app_key")]
        public string AppKey { get; set; }

        /// <summary>
        /// 应用秘钥
        /// </summary>
        [JsonPropertyName("app_secret")]
        public string AppSecret { get; set; }

        /// <summary>
        /// 开放平台Token
        /// </summary>
        [JsonPropertyName("open_token")]
        public string OpenToken { get; set; }

        /// <summary>
        /// 用户信息
        /// </summary>
        [JsonPropertyName("user_info")]
        public object UserInfo { get; set; }
    }

    /// <summary>
    /// 外部Token验证响应
    /// </summary>
    public class ExternalTokenValidateResponse : ExternalTokenResponse
    {
        /// <summary>
        /// Token是否有效
        /// </summary>
        [JsonPropertyName("is_valid")]
        public bool IsValid { get; set; }

        /// <summary>
        /// Token过期时间
        /// </summary>
        [JsonPropertyName("expires_at")]
        public DateTime? ExpiresAt { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        [JsonPropertyName("user_id")]
        public string UserId { get; set; }

        /// <summary>
        /// 作用域
        /// </summary>
        [JsonPropertyName("scope")]
        public string Scope { get; set; }
    }

    /// <summary>
    /// 外部Token使用记录查询响应
    /// </summary>
    public class ExternalTokenUsageResponse : ExternalTokenResponse
    {
        /// <summary>
        /// 使用记录列表
        /// </summary>
        [JsonPropertyName("usage_records")]
        public TokenUsageRecord[] UsageRecords { get; set; }

        /// <summary>
        /// 总记录数
        /// </summary>
        [JsonPropertyName("total_count")]
        public int TotalCount { get; set; }

        /// <summary>
        /// 当前页码
        /// </summary>
        [JsonPropertyName("page_number")]
        public int PageNumber { get; set; }

        /// <summary>
        /// 页面大小
        /// </summary>
        [JsonPropertyName("page_size")]
        public int PageSize { get; set; }
    }
} 