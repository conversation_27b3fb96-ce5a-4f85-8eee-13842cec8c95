version: '3.8'

services:
  chanjet-oauth:
    build: .
    container_name: chanjet-oauth-panel
    ports:
      - "35010:35010"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:35010
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:35010/api/token/status"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - chanjet-network

networks:
  chanjet-network:
    driver: bridge 