﻿using ChanjetOAuthTest.App.Configuration;
using ChanjetOAuthTest.App.Services;
using ChanjetOAuthTest.App.Interfaces;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using System;

namespace ChanjetOAuthTest
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        public void ConfigureServices(IServiceCollection services)
        {
            // 绑定appsettings.json中的Chanjet部分到ChanjetConfig类
            services.Configure<ChanjetConfig>(Configuration.GetSection("Chanjet"));
            
            // 绑定appsettings.json中的ExternalTokenService部分到ExternalTokenConfig类
            services.Configure<ExternalTokenConfig>(Configuration.GetSection("ExternalTokenService"));

            // 注册内存缓存服务
            services.AddMemoryCache();

            // 注册HttpClient服务
            services.AddHttpClient();

            // 注册Token存储服务
            services.AddSingleton<ITokenStorage, JsonTokenStorage>();
            
            // 注册Token管理服务
            services.AddScoped<ITokenManager, TokenManager>();

            // 注册数据同步服务
            services.AddSingleton<IDataSyncService, DataSyncService>();

            // 注册外部Token服务
            services.AddScoped<IExternalTokenService, ExternalTokenService>();

            // 添加Session支持，用于存储OAuth的state值
            services.AddDistributedMemoryCache();
            services.AddSession(options =>
            {
                options.IdleTimeout = TimeSpan.FromMinutes(20);
                options.Cookie.HttpOnly = true;
                options.Cookie.IsEssential = true;
            });

            services.AddControllers();
        }

        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }

            // 启用默认文件支持（支持index.html作为默认页面）
            app.UseDefaultFiles();
            
            // 启用静态文件中间件，支持访问wwwroot下的文件
            app.UseStaticFiles();

            app.UseRouting();

            // 启用Session中间件
            app.UseSession();

            app.UseAuthorization();

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
            });
        }
    }
}
