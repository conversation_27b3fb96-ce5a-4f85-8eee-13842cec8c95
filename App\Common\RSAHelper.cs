using System;
using System.Text;
using System.Security.Cryptography;
using System.Numerics;

namespace ChanjetOAuthTest.App.Common
{
    /// <summary>
    /// RSA加密助手类
    /// </summary>
    public class RSAHelper
    {
        /// <summary>
        /// 生成RSA密钥对
        /// </summary>
        /// <param name="PrivateKey">私钥</param>
        /// <param name="PublicKey">公钥</param>
        /// <param name="KeySize">密钥长度</param>
        public static void Generator(out string PrivateKey, out string PublicKey, int KeySize = 1024)
        {
            using (RSACryptoServiceProvider cryptoServiceProvider = new RSACryptoServiceProvider(KeySize))
            {
                PrivateKey = cryptoServiceProvider.ToXmlString(true);
                PublicKey = cryptoServiceProvider.ToXmlString(false);
            }
        }

        /// <summary>
        /// RSA加密
        /// </summary>
        /// <param name="content">待加密内容</param>
        /// <param name="publickey">公钥</param>
        /// <returns>加密后的Base64字符串</returns>
        public static string RSAEncryption(string content, string publickey)
        {
            try
            {
                using (RSACryptoServiceProvider cryptoServiceProvider = new RSACryptoServiceProvider())
                {
                    string xmlKey = RsaPublicKeyToXml(publickey);
                    if (string.IsNullOrEmpty(xmlKey) || xmlKey == "error")
                    {
                        throw new Exception("无法解析公钥格式");
                    }
                    cryptoServiceProvider.FromXmlString(xmlKey);
                    byte[] dataToEncrypt = Encoding.UTF8.GetBytes(content);
                    byte[] encryptedData = cryptoServiceProvider.Encrypt(dataToEncrypt, false);
                    return Convert.ToBase64String(encryptedData);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"RSA加密失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// RSA解密
        /// </summary>
        /// <param name="content">加密的Base64字符串</param>
        /// <param name="privatekey">私钥</param>
        /// <returns>解密后的字符串</returns>
        public static string RSADecrypt(string content, string privatekey)
        {
            try
            {
                using (RSACryptoServiceProvider cryptoServiceProvider = new RSACryptoServiceProvider())
                {
                    string xmlKey = RsaPrivateKeyToXml(privatekey);
                    if (string.IsNullOrEmpty(xmlKey) || xmlKey == "error")
                    {
                        throw new Exception("无法解析私钥格式");
                    }
                    cryptoServiceProvider.FromXmlString(xmlKey);
                    byte[] dataToDecrypt = Convert.FromBase64String(content);
                    byte[] decryptedData = cryptoServiceProvider.Decrypt(dataToDecrypt, false);
                    return Encoding.UTF8.GetString(decryptedData);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"RSA解密失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 将标准Base64格式的RSA公钥转换为XML格式
        /// </summary>
        /// <param name="publicKey">公钥</param>
        /// <returns>XML格式的公钥</returns>
        public static string RsaPublicKeyToXml(string publicKey)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(publicKey))
                    return "";
                
                if (publicKey.Contains("<RSAKeyValue>"))
                    return publicKey;

                // 尝试解析标准的Base64格式RSA公钥
                try
                {
                    byte[] keyBytes = Convert.FromBase64String(publicKey);
                    
                    // 解析DER格式的RSA公钥
                    var rsaParams = ParseRSAPublicKey(keyBytes);
                    
                    // 构造XML格式
                    return $"<RSAKeyValue><Modulus>{Convert.ToBase64String(rsaParams.Modulus)}</Modulus><Exponent>{Convert.ToBase64String(rsaParams.Exponent)}</Exponent></RSAKeyValue>";
                }
                catch
                {
                    // 如果Base64解析失败，尝试直接使用
                    return publicKey;
                }
            }
            catch
            {
                return "error";
            }
        }

        /// <summary>
        /// 将标准格式的RSA私钥转换为XML格式
        /// </summary>
        /// <param name="privateKey">私钥</param>
        /// <returns>XML格式的私钥</returns>
        public static string RsaPrivateKeyToXml(string privateKey)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(privateKey))
                    return "";
                
                if (privateKey.Contains("<RSAKeyValue>"))
                    return privateKey;

                // 简化处理：如果是其他格式，尝试直接使用
                return privateKey;
            }
            catch
            {
                return "error";
            }
        }

        /// <summary>
        /// 解析DER格式的RSA公钥
        /// </summary>
        /// <param name="keyBytes">密钥字节数组</param>
        /// <returns>RSA参数</returns>
        private static RSAParameters ParseRSAPublicKey(byte[] keyBytes)
        {
            try
            {
                // 简化的ASN.1 DER解析
                int offset = 0;
                
                // 跳过SEQUENCE标签和长度
                if (keyBytes[offset] == 0x30)
                {
                    offset++;
                    int seqLength = ReadLength(keyBytes, ref offset);
                }
                
                // 跳过AlgorithmIdentifier SEQUENCE
                if (keyBytes[offset] == 0x30)
                {
                    offset++;
                    int algIdLength = ReadLength(keyBytes, ref offset);
                    offset += algIdLength;
                }
                
                // 读取BIT STRING
                if (keyBytes[offset] == 0x03)
                {
                    offset++;
                    int bitStringLength = ReadLength(keyBytes, ref offset);
                    offset++; // 跳过未使用位数
                    
                    // 现在是RSA公钥的SEQUENCE
                    if (keyBytes[offset] == 0x30)
                    {
                        offset++;
                        int rsaSeqLength = ReadLength(keyBytes, ref offset);
                        
                        // 读取模数 (n)
                        if (keyBytes[offset] == 0x02)
                        {
                            offset++;
                            int modulusLength = ReadLength(keyBytes, ref offset);
                            byte[] modulus = new byte[modulusLength];
                            Array.Copy(keyBytes, offset, modulus, 0, modulusLength);
                            offset += modulusLength;
                            
                            // 读取指数 (e)
                            if (keyBytes[offset] == 0x02)
                            {
                                offset++;
                                int exponentLength = ReadLength(keyBytes, ref offset);
                                byte[] exponent = new byte[exponentLength];
                                Array.Copy(keyBytes, offset, exponent, 0, exponentLength);
                                
                                // 移除前导零字节
                                modulus = RemoveLeadingZeros(modulus);
                                exponent = RemoveLeadingZeros(exponent);
                                
                                return new RSAParameters
                                {
                                    Modulus = modulus,
                                    Exponent = exponent
                                };
                            }
                        }
                    }
                }
                
                throw new Exception("无法解析RSA公钥格式");
            }
            catch (Exception ex)
            {
                throw new Exception($"解析RSA公钥失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 读取ASN.1长度字段
        /// </summary>
        /// <param name="data">数据</param>
        /// <param name="offset">偏移量</param>
        /// <returns>长度值</returns>
        private static int ReadLength(byte[] data, ref int offset)
        {
            int length = data[offset++];
            if ((length & 0x80) == 0)
            {
                return length;
            }
            
            int lengthBytes = length & 0x7F;
            length = 0;
            for (int i = 0; i < lengthBytes; i++)
            {
                length = (length << 8) | data[offset++];
            }
            return length;
        }

        /// <summary>
        /// 移除字节数组前导零
        /// </summary>
        /// <param name="data">字节数组</param>
        /// <returns>处理后的字节数组</returns>
        private static byte[] RemoveLeadingZeros(byte[] data)
        {
            int index = 0;
            while (index < data.Length && data[index] == 0)
            {
                index++;
            }
            
            if (index == 0)
                return data;
                
            byte[] result = new byte[data.Length - index];
            Array.Copy(data, index, result, 0, result.Length);
            return result;
        }

        /// <summary>
        /// 生成数字签名
        /// </summary>
        /// <param name="originalText">原始文本</param>
        /// <param name="privateKey">私钥</param>
        /// <returns>签名的Base64字符串</returns>
        public static string GenSign(string originalText, string privateKey)
        {
            try
            {
                byte[] bytes = Encoding.UTF8.GetBytes(originalText);
                using (RSACryptoServiceProvider cryptoServiceProvider = new RSACryptoServiceProvider())
                {
                    string xmlKey = RsaPrivateKeyToXml(privateKey);
                    if (string.IsNullOrEmpty(xmlKey) || xmlKey == "error")
                    {
                        throw new Exception("无法解析私钥格式");
                    }
                    cryptoServiceProvider.FromXmlString(xmlKey);
                    using (SHA1CryptoServiceProvider sha1 = new SHA1CryptoServiceProvider())
                    {
                        byte[] signature = cryptoServiceProvider.SignData(bytes, sha1);
                        return Convert.ToBase64String(signature);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"生成签名失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 验证数字签名
        /// </summary>
        /// <param name="originalText">原始文本</param>
        /// <param name="signedData">签名数据</param>
        /// <param name="publicKey">公钥</param>
        /// <returns>验证结果</returns>
        public static bool VerifySigned(string originalText, string signedData, string publicKey)
        {
            try
            {
                using (RSACryptoServiceProvider cryptoServiceProvider = new RSACryptoServiceProvider())
                {
                    string xmlKey = RsaPublicKeyToXml(publicKey);
                    if (string.IsNullOrEmpty(xmlKey) || xmlKey == "error")
                    {
                        return false;
                    }
                    cryptoServiceProvider.FromXmlString(xmlKey);
                    byte[] bytes = Encoding.UTF8.GetBytes(originalText);
                    byte[] signature = Convert.FromBase64String(signedData);
                    using (SHA1CryptoServiceProvider sha1 = new SHA1CryptoServiceProvider())
                    {
                        return cryptoServiceProvider.VerifyData(bytes, sha1, signature);
                    }
                }
            }
            catch (Exception)
            {
                return false;
            }
        }
    }
} 