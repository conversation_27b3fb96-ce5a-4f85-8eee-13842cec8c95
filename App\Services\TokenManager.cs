using ChanjetOAuthTest.App.Models;
using ChanjetOAuthTest.App.Configuration;
using ChanjetOAuthTest.App.Interfaces;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Concurrent;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using System.Web;

namespace ChanjetOAuthTest.App.Services
{
    public class TokenManager : ITokenManager, IDisposable
    {
        private readonly HttpClient _httpClient;
        private readonly ChanjetConfig _config;
        private readonly ILogger<TokenManager> _logger;
        private readonly ITokenStorage _tokenStorage;
        private readonly ConcurrentDictionary<string, TokenInfo> _tokenCache;
        private readonly Timer _monitoringTimer;

        // API 地址常量
        private const string TokenUrl = "https://openapi.chanjet.com/auth/v2/getToken";
        private const string RefreshTokenUrl = "https://openapi.chanjet.com/auth/v2/refreshToken";

        // JSON 序列化选项
        private static readonly JsonSerializerOptions JsonOptions = new()
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false
        };

        public event EventHandler<TokenExpiredEventArgs> TokenExpired;
        public event EventHandler<TokenRefreshedEventArgs> TokenRefreshed;

        public TokenManager(HttpClient httpClient, IOptions<ChanjetConfig> config, ILogger<TokenManager> logger, ITokenStorage tokenStorage)
        {
            _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
            _config = config?.Value ?? throw new ArgumentNullException(nameof(config));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _tokenStorage = tokenStorage ?? throw new ArgumentNullException(nameof(tokenStorage));
            
            _tokenCache = new ConcurrentDictionary<string, TokenInfo>();
            
            // 启动时从持久化存储加载Token
            _ = Task.Run(LoadTokensFromStorageAsync);
            
            // 创建定时器，每分钟检查一次token状态
            _monitoringTimer = new Timer(MonitorTokens, null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
            
            _logger.LogInformation("TokenManager initialized with monitoring enabled");
        }

        /// <summary>
        /// 通过授权码获取Token（第一次获取）
        /// </summary>
        public async Task<TokenInfo> GetTokenByAuthCodeAsync(string authCode, string userId = null)
        {
            if (string.IsNullOrWhiteSpace(authCode))
                throw new ArgumentException("授权码不能为空", nameof(authCode));

            userId ??= "default";

            try
            {
                _logger.LogInformation("获取Token中，授权码: {AuthCode}, 用户ID: {UserId}", authCode, userId);

                var uriBuilder = new UriBuilder(TokenUrl);
                var query = HttpUtility.ParseQueryString(uriBuilder.Query);

                query["grantType"] = "authorization_code";
                query["code"] = authCode;
                query["redirectUri"] = _config.RedirectUri;
                uriBuilder.Query = query.ToString();

                var request = new HttpRequestMessage(HttpMethod.Get, uriBuilder.Uri);
                AddAuthHeaders(request);

                var response = await _httpClient.SendAsync(request);
                var json = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogError("获取Token失败: {StatusCode} - {Response}", response.StatusCode, json);
                    throw new HttpRequestException($"获取Token失败: {response.StatusCode} - {json}");
                }

                // 解析响应
                var jsonDocument = JsonDocument.Parse(json);
                if (!jsonDocument.RootElement.TryGetProperty("result", out var resultElement))
                {
                    throw new InvalidOperationException($"API响应格式异常，缺少result字段: {json}");
                }

                var tokenResponse = JsonSerializer.Deserialize<RefreshTokenResponse>(resultElement.GetRawText(), JsonOptions);
                
                // 转换为TokenInfo并计算过期时间
                var tokenInfo = new TokenInfo
                {
                    AccessToken = tokenResponse.AccessToken,
                    RefreshToken = tokenResponse.RefreshToken,
                    ExpiresIn = tokenResponse.ExpiresIn,
                    RefreshExpiresIn = tokenResponse.RefreshExpiresIn,
                    TokenType = tokenResponse.TokenType,
                    Scope = tokenResponse.Scope,
                    UserId = userId,
                    UserAuthPermanentCode = tokenResponse.UserAuthPermanentCode
                };

                tokenInfo.CalculateExpirationTimes();

                // 缓存Token到内存和持久化存储
                _tokenCache.AddOrUpdate(userId, tokenInfo, (key, oldValue) => tokenInfo);
                await _tokenStorage.SaveTokenAsync(userId, tokenInfo);

                _logger.LogInformation("Token获取成功，用户ID: {UserId}, 过期时间: {ExpiresAt}", userId, tokenInfo.ExpiresAt);

                return tokenInfo;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取Token时发生异常，用户ID: {UserId}", userId);
                throw;
            }
        }

        /// <summary>
        /// 获取有效的Token（自动刷新过期的Token）
        /// </summary>
        public async Task<TokenInfo> GetValidTokenAsync(string userId)
        {
            userId ??= "default";

            if (!_tokenCache.TryGetValue(userId, out var tokenInfo))
            {
                // 如果内存缓存中没有，尝试从持久化存储加载
                tokenInfo = await _tokenStorage.GetTokenAsync(userId);
                if (tokenInfo != null)
                {
                    _tokenCache.TryAdd(userId, tokenInfo);
                    _logger.LogInformation("从持久化存储加载Token到内存缓存，用户ID: {UserId}", userId);
                }
                else
                {
                    _logger.LogWarning("未找到用户Token，用户ID: {UserId}", userId);
                    return null;
                }
            }

            // 检查RefreshToken是否过期
            if (tokenInfo.IsRefreshTokenExpired)
            {
                _logger.LogWarning("RefreshToken已过期，需要重新授权，用户ID: {UserId}", userId);
                _tokenCache.TryRemove(userId, out _);
                await _tokenStorage.RemoveTokenAsync(userId);
                TokenExpired?.Invoke(this, new TokenExpiredEventArgs { UserId = userId, ExpiredToken = tokenInfo });
                return null;
            }

            // 检查AccessToken是否需要刷新
            if (tokenInfo.IsAccessTokenExpiring || tokenInfo.IsAccessTokenExpired)
            {
                _logger.LogInformation("AccessToken即将过期或已过期，开始刷新，用户ID: {UserId}", userId);
                return await RefreshTokenAsync(userId);
            }

            return tokenInfo;
        }

        /// <summary>
        /// 获取Token信息（不触发刷新，直接返回存储的Token信息）
        /// </summary>
        public async Task<TokenInfo> GetTokenInfoAsync(string userId)
        {
            userId ??= "default";

            // 优先从内存缓存获取
            if (_tokenCache.TryGetValue(userId, out var cachedToken))
            {
                _logger.LogDebug("从内存缓存获取Token信息，用户ID: {UserId}", userId);
                return cachedToken;
            }

            // 如果内存中没有，尝试从持久化存储获取
            try
            {
                var storedToken = await _tokenStorage.GetTokenAsync(userId);
                if (storedToken != null)
                {
                    // 将从存储加载的Token放入内存缓存
                    _tokenCache.TryAdd(userId, storedToken);
                    _logger.LogDebug("从持久化存储获取Token信息，用户ID: {UserId}", userId);
                    return storedToken;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "从持久化存储获取Token信息失败，用户ID: {UserId}", userId);
            }

            _logger.LogWarning("未找到Token信息，用户ID: {UserId}", userId);
            return null;
        }

        /// <summary>
        /// 刷新Token
        /// </summary>
        public async Task<TokenInfo> RefreshTokenAsync(string userId)
        {
            userId ??= "default";

            if (!_tokenCache.TryGetValue(userId, out var currentToken))
            {
                // 如果内存缓存中没有，尝试从持久化存储加载
                currentToken = await _tokenStorage.GetTokenAsync(userId);
                if (currentToken != null)
                {
                    _tokenCache.TryAdd(userId, currentToken);
                    _logger.LogInformation("从持久化存储加载Token到内存缓存用于刷新，用户ID: {UserId}", userId);
                }
                else
                {
                    _logger.LogWarning("未找到需要刷新的Token，用户ID: {UserId}", userId);
                    throw new InvalidOperationException($"未找到用户Token，用户ID: {userId}");
                }
            }

            if (currentToken.IsRefreshTokenExpired)
            {
                _logger.LogWarning("RefreshToken已过期，无法刷新，用户ID: {UserId}", userId);
                _tokenCache.TryRemove(userId, out _);
                await _tokenStorage.RemoveTokenAsync(userId);
                TokenExpired?.Invoke(this, new TokenExpiredEventArgs { UserId = userId, ExpiredToken = currentToken });
                throw new InvalidOperationException("RefreshToken已过期，需要重新授权");
            }

            try
            {
                _logger.LogInformation("开始刷新Token，用户ID: {UserId}", userId);

                var uriBuilder = new UriBuilder(RefreshTokenUrl);
                var query = HttpUtility.ParseQueryString(uriBuilder.Query);

                query["grantType"] = "refresh_token";
                query["refreshToken"] = currentToken.RefreshToken;
                uriBuilder.Query = query.ToString();

                var request = new HttpRequestMessage(HttpMethod.Get, uriBuilder.Uri);
                AddAuthHeaders(request);

                var response = await _httpClient.SendAsync(request);
                var json = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogError("刷新Token失败: {StatusCode} - {Response}", response.StatusCode, json);
                    
                    // 如果刷新失败，可能是RefreshToken无效，移除缓存并触发过期事件
                    _tokenCache.TryRemove(userId, out _);
                    await _tokenStorage.RemoveTokenAsync(userId);
                    TokenExpired?.Invoke(this, new TokenExpiredEventArgs { UserId = userId, ExpiredToken = currentToken });
                    
                    throw new HttpRequestException($"刷新Token失败: {response.StatusCode} - {json}");
                }

                // 解析响应
                var jsonDocument = JsonDocument.Parse(json);
                if (!jsonDocument.RootElement.TryGetProperty("result", out var resultElement))
                {
                    throw new InvalidOperationException($"API响应格式异常，缺少result字段: {json}");
                }

                var tokenResponse = JsonSerializer.Deserialize<RefreshTokenResponse>(resultElement.GetRawText(), JsonOptions);

                // 创建新的TokenInfo
                var newTokenInfo = new TokenInfo
                {
                    AccessToken = tokenResponse.AccessToken,
                    RefreshToken = tokenResponse.RefreshToken,
                    ExpiresIn = tokenResponse.ExpiresIn,
                    RefreshExpiresIn = tokenResponse.RefreshExpiresIn,
                    TokenType = tokenResponse.TokenType,
                    Scope = tokenResponse.Scope,
                    UserId = userId
                };

                newTokenInfo.CalculateExpirationTimes();

                // 更新缓存和持久化存储
                _tokenCache.AddOrUpdate(userId, newTokenInfo, (key, oldValue) => newTokenInfo);
                await _tokenStorage.SaveTokenAsync(userId, newTokenInfo);

                _logger.LogInformation("Token刷新成功，用户ID: {UserId}, 新过期时间: {ExpiresAt}", userId, newTokenInfo.ExpiresAt);

                // 触发刷新事件
                TokenRefreshed?.Invoke(this, new TokenRefreshedEventArgs 
                { 
                    UserId = userId, 
                    OldToken = currentToken, 
                    NewToken = newTokenInfo 
                });

                return newTokenInfo;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新Token时发生异常，用户ID: {UserId}", userId);
                throw;
            }
        }

        /// <summary>
        /// 检查Token是否有效
        /// </summary>
        public async Task<bool> IsTokenValidAsync(string userId)
        {
            var token = await GetValidTokenAsync(userId);
            return token != null && !token.IsAccessTokenExpired;
        }

        /// <summary>
        /// 开始Token监控
        /// </summary>
        public void StartTokenMonitoring()
        {
            _logger.LogInformation("Token监控已启动");
        }

        /// <summary>
        /// 停止Token监控
        /// </summary>
        public void StopTokenMonitoring()
        {
            _monitoringTimer?.Change(Timeout.Infinite, Timeout.Infinite);
            _logger.LogInformation("Token监控已停止");
        }

        /// <summary>
        /// 监控所有Token的状态
        /// </summary>
        private async void MonitorTokens(object state)
        {
            if (_tokenCache.IsEmpty)
                return;

            _logger.LogDebug("开始监控Token状态，缓存数量: {Count}", _tokenCache.Count);

            foreach (var kvp in _tokenCache)
            {
                var userId = kvp.Key;
                var tokenInfo = kvp.Value;

                try
                {
                    // 检查是否需要刷新
                    if (tokenInfo.IsAccessTokenExpiring && !tokenInfo.IsRefreshTokenExpired)
                    {
                        _logger.LogInformation("检测到Token即将过期，开始自动刷新，用户ID: {UserId}", userId);
                        await RefreshTokenAsync(userId);
                    }
                    // 检查RefreshToken是否过期
                    else if (tokenInfo.IsRefreshTokenExpired)
                    {
                        _logger.LogWarning("检测到RefreshToken过期，移除缓存，用户ID: {UserId}", userId);
                        _tokenCache.TryRemove(userId, out _);
                        await _tokenStorage.RemoveTokenAsync(userId);
                        TokenExpired?.Invoke(this, new TokenExpiredEventArgs { UserId = userId, ExpiredToken = tokenInfo });
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "自动刷新Token失败，用户ID: {UserId}", userId);
                }
            }
        }

        /// <summary>
        /// 添加认证头
        /// </summary>
        private void AddAuthHeaders(HttpRequestMessage request)
        {
            request.Headers.Add("appKey", _config.AppKey);
            request.Headers.Add("appSecret", _config.AppSecret);
            // Content-Type 应该在Content对象上设置，不是在Headers中
        }

        /// <summary>
        /// 从持久化存储加载所有Token到内存缓存
        /// </summary>
        private async Task LoadTokensFromStorageAsync()
        {
            try
            {
                var tokens = await _tokenStorage.GetAllTokensAsync();
                foreach (var kvp in tokens)
                {
                    _tokenCache.TryAdd(kvp.Key, kvp.Value);
                }
                
                _logger.LogInformation("从持久化存储加载Token完成，数量: {Count}", tokens.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "从持久化存储加载Token失败");
            }
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        public void Dispose()
        {
            _monitoringTimer?.Dispose();
            _tokenCache?.Clear();
            _logger.LogInformation("TokenManager已释放资源");
        }
    }
} 