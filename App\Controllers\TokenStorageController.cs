using ChanjetOAuthTest.App.Services;
using ChanjetOAuthTest.App.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace ChanjetOAuthTest.App.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class TokenStorageController : ControllerBase
    {
        private readonly ITokenStorage _tokenStorage;
        private readonly ILogger<TokenStorageController> _logger;

        public TokenStorageController(ITokenStorage tokenStorage, ILogger<TokenStorageController> logger)
        {
            _tokenStorage = tokenStorage ?? throw new ArgumentNullException(nameof(tokenStorage));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 获取所有存储的Token信息
        /// </summary>
        [HttpGet("all")]
        public async Task<IActionResult> GetAllTokens()
        {
            try
            {
                _logger.LogInformation("收到获取所有Token的请求");

                var tokens = await _tokenStorage.GetAllTokensAsync();

                return Ok(new
                {
                    success = true,
                    message = "获取Token列表成功",
                    data = new
                    {
                        count = tokens.Count,
                        tokens = tokens
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有Token失败");
                return StatusCode(500, new
                {
                    success = false,
                    error = "获取Token列表失败",
                    message = ex.Message
                });
            }
        }

        /// <summary>
        /// 清空所有Token
        /// </summary>
        [HttpDelete("all")]
        public async Task<IActionResult> ClearAllTokens()
        {
            try
            {
                _logger.LogInformation("收到清空所有Token的请求");

                await _tokenStorage.ClearAllTokensAsync();

                return Ok(new
                {
                    success = true,
                    message = "所有Token已清空"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清空所有Token失败");
                return StatusCode(500, new
                {
                    success = false,
                    error = "清空Token失败",
                    message = ex.Message
                });
            }
        }
    }
} 