using ChanjetOAuthTest.App.Models;
using ChanjetOAuthTest.App.Services;
using ChanjetOAuthTest.App.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Net.Http;
using System.Threading.Tasks;

namespace ChanjetOAuthTest.App.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class TokenController : ControllerBase
    {
        private readonly ITokenManager _tokenManager;
        private readonly ILogger<TokenController> _logger;

        public TokenController(ITokenManager tokenManager, ILogger<TokenController> logger)
        {
            _tokenManager = tokenManager;
            _logger = logger;
        }

        /// <summary>
        /// 通过授权码获取Token
        /// 注意：授权码10分钟内有效，且仅能使用一次
        /// </summary>
        /// <param name="authCode">授权码（code参数，10分钟内有效，仅能使用一次）</param>
        /// <param name="userId">用户ID（可选，用于区分不同用户的token）</param>
        /// <returns>Token信息，包含user_auth_permanent_code用于长期授权</returns>
        [HttpPost("get-token")]
        public async Task<IActionResult> GetToken([FromQuery] string authCode, [FromQuery] string userId = null)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(authCode))
                {
                    return BadRequest(new 
                    { 
                        success = false,
                        error = "授权码不能为空",
                        message = "请提供有效的授权码（code参数）",
                        error_code = "MISSING_AUTH_CODE"
                    });
                }

                // 验证授权码格式（基本验证）
                if (authCode.Length < 10)
                {
                    return BadRequest(new 
                    { 
                        success = false,
                        error = "授权码格式无效",
                        message = "授权码长度过短，请检查是否为有效的授权码",
                        error_code = "INVALID_AUTH_CODE_FORMAT"
                    });
                }

                _logger.LogInformation("收到获取Token请求，授权码: {AuthCode}, 用户ID: {UserId}", authCode, userId);

                var tokenInfo = await _tokenManager.GetTokenByAuthCodeAsync(authCode, userId);

                var response = new TokenResponse
                {
                    Success = true,
                    Message = "Token获取成功",
                    Data = new TokenData
                    {
                        AccessToken = tokenInfo.AccessToken,
                        RefreshToken = tokenInfo.RefreshToken,
                        ExpiresIn = tokenInfo.ExpiresIn,
                        RefreshExpiresIn = tokenInfo.RefreshExpiresIn,
                        TokenType = tokenInfo.TokenType,
                        Scope = tokenInfo.Scope,
                        ExpiresAt = tokenInfo.ExpiresAt,
                        RefreshExpiresAt = tokenInfo.RefreshExpiresAt,
                        UserId = tokenInfo.UserId,
                        UserAuthPermanentCode = tokenInfo.UserAuthPermanentCode
                    },
                    Tips = new TokenTips
                    {
                        AccessTokenDesc = "接口调用凭证（即openToken），有效期默认6天",
                        RefreshTokenDesc = "刷新凭证（用于后续刷新access_token）",
                        UserAuthPermanentCodeDesc = "用户永久授权码（建议存储，用于长期获取token）",
                        Suggestion = "建议保存user_auth_permanent_code，以便后续通过用户永久授权码直接获取新token（无需用户重复授权）"
                    }
                };

                return Ok(response);
            }
            catch (HttpRequestException ex) when (ex.Message.Contains("4001") || ex.Message.Contains("4002"))
            {
                _logger.LogError(ex, "获取Token失败 - appKey错误");
                return BadRequest(new 
                { 
                    success = false, 
                    error = "应用配置错误", 
                    message = "请检查appKey是否正确（错误码4001/4002）",
                    error_code = "INVALID_APP_KEY",
                    suggestion = "请联系管理员检查应用配置"
                });
            }
            catch (HttpRequestException ex) when (ex.Message.Contains("authorization_code"))
            {
                _logger.LogError(ex, "获取Token失败 - 授权码无效");
                return BadRequest(new 
                { 
                    success = false, 
                    error = "授权码无效或已过期", 
                    message = "授权码可能已过期（10分钟有效期）或已被使用",
                    error_code = "INVALID_AUTH_CODE",
                    suggestion = "请重新引导用户授权获取新的授权码"
                });
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError(ex, "获取Token失败 - HTTP请求异常");
                return StatusCode(502, new 
                { 
                    success = false, 
                    error = "调用畅捷通API失败", 
                    message = ex.Message,
                    error_code = "API_REQUEST_FAILED",
                    suggestion = "请稍后重试，或检查网络连接"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取Token失败 - 系统异常");
                return StatusCode(500, new 
                { 
                    success = false, 
                    error = "系统内部错误", 
                    message = ex.Message,
                    error_code = "INTERNAL_ERROR",
                    suggestion = "请联系技术支持"
                });
            }
        }

        /// <summary>
        /// 获取有效的Token（如果过期会自动刷新）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>有效的Token信息</returns>
        [HttpGet("valid-token")]
        public async Task<IActionResult> GetValidToken([FromQuery] string userId = "default")
        {
            try
            {
                _logger.LogInformation("收到获取有效Token请求，用户ID: {UserId}", userId);

                var tokenInfo = await _tokenManager.GetValidTokenAsync(userId);

                if (tokenInfo == null)
                {
                    return NotFound(new 
                    { 
                        success = false, 
                        error = "未找到Token或Token已过期", 
                        message = "请重新授权获取Token" 
                    });
                }

                return Ok(new
                {
                    success = true,
                    message = "Token获取成功",
                    data = new
                    {
                        access_token = tokenInfo.AccessToken,
                        expires_in = tokenInfo.ExpiresIn,
                        token_type = tokenInfo.TokenType,
                        scope = tokenInfo.Scope,
                        expires_at = tokenInfo.ExpiresAt,
                        user_id = tokenInfo.UserId,
                        is_refreshed = tokenInfo.CreatedAt > DateTime.UtcNow.AddMinutes(-5) // 判断是否是最近刷新的
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取有效Token失败，用户ID: {UserId}", userId);
                return StatusCode(500, new 
                { 
                    success = false, 
                    error = "获取有效Token失败", 
                    message = ex.Message 
                });
            }
        }

        /// <summary>
        /// 手动刷新Token
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>刷新后的Token信息</returns>
        [HttpPost("refresh")]
        public async Task<IActionResult> RefreshToken([FromQuery] string userId = "default")
        {
            try
            {
                _logger.LogInformation("收到手动刷新Token请求，用户ID: {UserId}", userId);

                var tokenInfo = await _tokenManager.RefreshTokenAsync(userId);

                return Ok(new
                {
                    success = true,
                    message = "Token刷新成功",
                    data = new
                    {
                        access_token = tokenInfo.AccessToken,
                        refresh_token = tokenInfo.RefreshToken,
                        expires_in = tokenInfo.ExpiresIn,
                        refresh_expires_in = tokenInfo.RefreshExpiresIn,
                        token_type = tokenInfo.TokenType,
                        scope = tokenInfo.Scope,
                        expires_at = tokenInfo.ExpiresAt,
                        refresh_expires_at = tokenInfo.RefreshExpiresAt,
                        user_id = tokenInfo.UserId,
                        refreshed_at = tokenInfo.CreatedAt
                    }
                });
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "刷新Token失败，用户ID: {UserId}", userId);
                return BadRequest(new 
                { 
                    success = false, 
                    error = "刷新Token失败", 
                    message = ex.Message 
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新Token时发生异常，用户ID: {UserId}", userId);
                return StatusCode(500, new 
                { 
                    success = false, 
                    error = "刷新Token失败", 
                    message = ex.Message 
                });
            }
        }

        /// <summary>
        /// 获取完整Token信息（包括refresh_token，不会触发刷新）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>完整的Token信息</returns>
        [HttpGet("info")]
        public async Task<IActionResult> GetTokenInfo([FromQuery] string userId = "default")
        {
            try
            {
                _logger.LogInformation("收到获取Token信息请求，用户ID: {UserId}", userId);

                // 直接获取Token信息，不触发刷新
                var tokenInfo = await _tokenManager.GetTokenInfoAsync(userId);

                if (tokenInfo == null)
                {
                    return NotFound(new 
                    { 
                        success = false, 
                        error = "未找到Token", 
                        message = "请重新授权获取Token" 
                    });
                }

                return Ok(new
                {
                    success = true,
                    message = "Token信息获取成功",
                    data = new
                    {
                        access_token = tokenInfo.AccessToken,
                        refresh_token = tokenInfo.RefreshToken,
                        expires_in = tokenInfo.ExpiresIn,
                        refresh_expires_in = tokenInfo.RefreshExpiresIn,
                        token_type = tokenInfo.TokenType,
                        scope = tokenInfo.Scope,
                        expires_at = tokenInfo.ExpiresAt,
                        refresh_expires_at = tokenInfo.RefreshExpiresAt,
                        created_at = tokenInfo.CreatedAt,
                        user_id = tokenInfo.UserId,
                        user_auth_permanent_code = tokenInfo.UserAuthPermanentCode,
                        is_access_token_expired = tokenInfo.IsAccessTokenExpired,
                        is_access_token_expiring = tokenInfo.IsAccessTokenExpiring,
                        is_refresh_token_expired = tokenInfo.IsRefreshTokenExpired
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取Token信息失败，用户ID: {UserId}", userId);
                return StatusCode(500, new 
                { 
                    success = false, 
                    error = "获取Token信息失败", 
                    message = ex.Message 
                });
            }
        }

        /// <summary>
        /// 检查Token状态
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>Token状态信息</returns>
        [HttpGet("status")]
        public async Task<IActionResult> GetTokenStatus([FromQuery] string userId = "default")
        {
            try
            {
                _logger.LogInformation("收到Token状态查询请求，用户ID: {UserId}", userId);

                var isValid = await _tokenManager.IsTokenValidAsync(userId);

                // 尝试获取Token信息（不刷新）
                var tokenInfo = await _tokenManager.GetValidTokenAsync(userId);

                if (tokenInfo == null)
                {
                    return Ok(new
                    {
                        success = true,
                        data = new
                        {
                            user_id = userId,
                            has_token = false,
                            is_valid = false,
                            status = "no_token",
                            message = "未找到Token"
                        }
                    });
                }

                var now = DateTime.UtcNow;
                var status = "valid";
                var message = "Token有效";

                if (tokenInfo.IsAccessTokenExpired)
                {
                    status = "access_expired";
                    message = "AccessToken已过期";
                }
                else if (tokenInfo.IsAccessTokenExpiring)
                {
                    status = "access_expiring";
                    message = "AccessToken即将过期";
                }

                if (tokenInfo.IsRefreshTokenExpired)
                {
                    status = "refresh_expired";
                    message = "RefreshToken已过期，需要重新授权";
                }

                return Ok(new
                {
                    success = true,
                    data = new
                    {
                        user_id = userId,
                        has_token = true,
                        is_valid = isValid,
                        status = status,
                        message = message,
                        token_info = new
                        {
                            expires_at = tokenInfo.ExpiresAt,
                            refresh_expires_at = tokenInfo.RefreshExpiresAt,
                            created_at = tokenInfo.CreatedAt,
                            time_to_expiry = (tokenInfo.ExpiresAt - now).TotalMinutes,
                            time_to_refresh_expiry = (tokenInfo.RefreshExpiresAt - now).TotalDays
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "查询Token状态失败，用户ID: {UserId}", userId);
                return StatusCode(500, new 
                { 
                    success = false, 
                    error = "查询Token状态失败", 
                    message = ex.Message 
                });
            }
        }

        /// <summary>
        /// 测试接口 - 使用Token调用API
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>API调用结果</returns>
        [HttpGet("test-api")]
        public async Task<IActionResult> TestApi([FromQuery] string userId = "default")
        {
            try
            {
                _logger.LogInformation("收到测试API请求，用户ID: {UserId}", userId);

                // 获取有效的Token
                var tokenInfo = await _tokenManager.GetValidTokenAsync(userId);

                if (tokenInfo == null)
                {
                    return Unauthorized(new 
                    { 
                        success = false, 
                        error = "未找到有效Token", 
                        message = "请重新授权获取Token" 
                    });
                }

                // 这里可以使用tokenInfo.AccessToken调用实际的API
                // 为了演示，我们只返回Token信息
                return Ok(new
                {
                    success = true,
                    message = "Token验证成功，可以调用API",
                    data = new
                    {
                        token_used = tokenInfo.AccessToken[..10] + "...", // 只显示前10位
                        token_type = tokenInfo.TokenType,
                        expires_at = tokenInfo.ExpiresAt,
                        user_id = tokenInfo.UserId,
                        api_call_time = DateTime.UtcNow
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "测试API调用失败，用户ID: {UserId}", userId);
                return StatusCode(500, new 
                { 
                    success = false, 
                    error = "测试API调用失败", 
                    message = ex.Message 
                });
            }
        }
    }
} 