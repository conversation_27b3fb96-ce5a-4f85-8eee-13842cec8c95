using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using ChanjetOAuthTest.App.Interfaces;
using ChanjetOAuthTest.App.Models;
using System;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace ChanjetOAuthTest.App.Controllers
{
    /// <summary>
    /// 外部Token API控制器
    /// </summary>
    [ApiController]
    [Route("api/external/token")]
    [Produces("application/json")]
    public class ExternalTokenController : ControllerBase
    {
        private readonly IExternalTokenService _externalTokenService;
        private readonly ILogger<ExternalTokenController> _logger;

        public ExternalTokenController(
            IExternalTokenService externalTokenService,
            ILogger<ExternalTokenController> logger)
        {
            _externalTokenService = externalTokenService ?? throw new ArgumentNullException(nameof(externalTokenService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 获取Token
        /// </summary>
        /// <param name="request">Token获取请求</param>
        /// <returns>Token获取响应，包含AppKey、AppSecret、OpenToken等信息</returns>
        [HttpPost("acquire")]
        public async Task<ActionResult<ExternalTokenAcquireResponse>> AcquireToken([FromBody] ExternalTokenRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ExternalTokenAcquireResponse
                    {
                        Success = false,
                        ErrorCode = "INVALID_REQUEST",
                        ErrorMessage = "请求参数验证失败"
                    });
                }

                var clientInfo = GetClientInfo();
                var response = await _externalTokenService.AcquireTokenAsync(request, clientInfo);
                
                return response.Success ? Ok(response) : BadRequest(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理Token获取请求时发生异常");
                return StatusCode(500, new ExternalTokenAcquireResponse
                {
                    Success = false,
                    ErrorCode = "INTERNAL_ERROR",
                    ErrorMessage = "服务内部错误"
                });
            }
        }

        /// <summary>
        /// 刷新Token
        /// </summary>
        /// <param name="request">Token刷新请求</param>
        /// <returns>Token刷新响应，包含新的AppKey、AppSecret、OpenToken等信息</returns>
        [HttpPost("refresh")]
        public async Task<ActionResult<ExternalTokenAcquireResponse>> RefreshToken([FromBody] ExternalTokenRefreshRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ExternalTokenAcquireResponse
                    {
                        Success = false,
                        ErrorCode = "INVALID_REQUEST",
                        ErrorMessage = "请求参数验证失败"
                    });
                }

                var clientInfo = GetClientInfo();
                var response = await _externalTokenService.RefreshTokenAsync(request, clientInfo);
                
                return response.Success ? Ok(response) : BadRequest(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理Token刷新请求时发生异常");
                return StatusCode(500, new ExternalTokenAcquireResponse
                {
                    Success = false,
                    ErrorCode = "INTERNAL_ERROR",
                    ErrorMessage = "服务内部错误"
                });
            }
        }

        /// <summary>
        /// 验证Token
        /// </summary>
        /// <param name="request">Token验证请求</param>
        /// <returns>Token验证结果</returns>
        [HttpPost("validate")]
        public async Task<ActionResult<ExternalTokenValidateResponse>> ValidateToken([FromBody] ExternalTokenValidateRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ExternalTokenValidateResponse
                    {
                        Success = false,
                        ErrorCode = "INVALID_REQUEST",
                        ErrorMessage = "请求参数验证失败"
                    });
                }

                var clientInfo = GetClientInfo();
                var response = await _externalTokenService.ValidateTokenAsync(request, clientInfo);
                
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理Token验证请求时发生异常");
                return StatusCode(500, new ExternalTokenValidateResponse
                {
                    Success = false,
                    ErrorCode = "INTERNAL_ERROR",
                    ErrorMessage = "服务内部错误"
                });
            }
        }

        /// <summary>
        /// 撤销Token
        /// </summary>
        /// <param name="request">Token撤销请求</param>
        /// <returns>撤销结果</returns>
        [HttpPost("revoke")]
        public async Task<ActionResult<ExternalTokenResponse>> RevokeToken([FromBody] RevokeTokenRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ExternalTokenResponse
                    {
                        Success = false,
                        ErrorCode = "INVALID_REQUEST",
                        ErrorMessage = "请求参数验证失败"
                    });
                }

                var clientInfo = GetClientInfo();
                var response = await _externalTokenService.RevokeTokenAsync(
                    request.UserId, 
                    request.ClientId, 
                    request.ClientSecret, 
                    clientInfo);
                
                return response.Success ? Ok(response) : BadRequest(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理Token撤销请求时发生异常");
                return StatusCode(500, new ExternalTokenResponse
                {
                    Success = false,
                    ErrorCode = "INTERNAL_ERROR",
                    ErrorMessage = "服务内部错误"
                });
            }
        }

        /// <summary>
        /// 获取Token使用记录
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="clientId">客户端ID（可选）</param>
        /// <param name="startTime">开始时间（可选）</param>
        /// <param name="endTime">结束时间（可选）</param>
        /// <param name="pageNumber">页码，默认1</param>
        /// <param name="pageSize">页面大小，默认20</param>
        /// <returns>使用记录列表</returns>
        [HttpGet("usage-records")]
        public async Task<ActionResult<ExternalTokenUsageResponse>> GetUsageRecords(
            [FromQuery, Required] string userId,
            [FromQuery] string clientId = null,
            [FromQuery] DateTime? startTime = null,
            [FromQuery] DateTime? endTime = null,
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 20)
        {
            try
            {
                if (pageNumber < 1 || pageSize < 1 || pageSize > 100)
                {
                    return BadRequest(new ExternalTokenUsageResponse
                    {
                        Success = false,
                        ErrorCode = "INVALID_PARAMETERS",
                        ErrorMessage = "页码必须大于0，页面大小必须在1-100之间"
                    });
                }

                var response = await _externalTokenService.GetTokenUsageRecordsAsync(
                    userId, clientId, startTime, endTime, pageNumber, pageSize);
                
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取Token使用记录时发生异常");
                return StatusCode(500, new ExternalTokenUsageResponse
                {
                    Success = false,
                    ErrorCode = "INTERNAL_ERROR",
                    ErrorMessage = "服务内部错误"
                });
            }
        }

        /// <summary>
        /// 获取Token使用统计
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="clientId">客户端ID（可选）</param>
        /// <param name="days">统计天数，默认30天</param>
        /// <returns>使用统计信息</returns>
        [HttpGet("usage-statistics")]
        public async Task<ActionResult<TokenUsageStatistics>> GetUsageStatistics(
            [FromQuery, Required] string userId,
            [FromQuery] string clientId = null,
            [FromQuery] int days = 30)
        {
            try
            {
                if (days < 1 || days > 365)
                {
                    return BadRequest(new
                    {
                        success = false,
                        error_code = "INVALID_PARAMETERS",
                        error_message = "统计天数必须在1-365之间"
                    });
                }

                var statistics = await _externalTokenService.GetTokenUsageStatisticsAsync(userId, clientId, days);
                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取Token使用统计时发生异常");
                return StatusCode(500, new
                {
                    success = false,
                    error_code = "INTERNAL_ERROR",
                    error_message = "服务内部错误"
                });
            }
        }

        /// <summary>
        /// 获取客户端凭据列表
        /// </summary>
        /// <returns>客户端凭据列表</returns>
        [HttpGet("client-credentials")]
        public async Task<ActionResult<object>> GetClientCredentials()
        {
            try
            {
                var credentials = await _externalTokenService.GetClientCredentialsAsync();
                return Ok(new
                {
                    success = true,
                    data = credentials.Select(c => new
                    {
                        clientId = c.ClientId,
                        clientName = c.ClientName
                    })
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取客户端凭据列表时发生异常");
                return StatusCode(500, new
                {
                    success = false,
                    error_code = "INTERNAL_ERROR",
                    error_message = "服务内部错误"
                });
            }
        }

        /// <summary>
        /// 健康检查接口
        /// </summary>
        /// <returns>服务状态</returns>
        [HttpGet("health")]
        public ActionResult<object> HealthCheck()
        {
            return Ok(new
            {
                status = "healthy",
                timestamp = DateTime.UtcNow,
                service = "External Token Service"
            });
        }

        /// <summary>
        /// 获取客户端信息
        /// </summary>
        /// <returns>客户端信息</returns>
        private ClientInfo GetClientInfo()
        {
            return new ClientInfo
            {
                IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString(),
                UserAgent = Request.Headers["User-Agent"].ToString(),
                RequestTime = DateTime.UtcNow
            };
        }
    }

    /// <summary>
    /// Token撤销请求模型
    /// </summary>
    public class RevokeTokenRequest
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Required(ErrorMessage = "用户ID不能为空")]
        public string UserId { get; set; }

        /// <summary>
        /// 客户端ID
        /// </summary>
        [Required(ErrorMessage = "客户端ID不能为空")]
        public string ClientId { get; set; }

        /// <summary>
        /// 客户端密钥
        /// </summary>
        [Required(ErrorMessage = "客户端密钥不能为空")]
        public string ClientSecret { get; set; }
    }
} 