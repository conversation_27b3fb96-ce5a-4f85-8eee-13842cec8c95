<template>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold tracking-tight">数据同步</h1>
        <p class="text-muted-foreground">管理系统间的数据同步任务</p>
      </div>
      <div class="flex space-x-2">
        <Button @click="refreshSyncJobs" :disabled="loading" variant="outline">
          <RefreshCw class="mr-2 h-4 w-4" :class="{ 'animate-spin': loading }" />
          刷新
        </Button>
        <Button @click="startSync">
          <Play class="mr-2 h-4 w-4" />
          立即同步
        </Button>
      </div>
    </div>

    <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">同步任务</CardTitle>
          <Database class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ syncStats.total }}</div>
          <p class="text-xs text-muted-foreground">总计</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">运行中</CardTitle>
          <Play class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold text-blue-600">{{ syncStats.running }}</div>
          <p class="text-xs text-muted-foreground">正在执行</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">已完成</CardTitle>
          <CheckCircle class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold text-green-600">{{ syncStats.completed }}</div>
          <p class="text-xs text-muted-foreground">成功同步</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">失败</CardTitle>
          <XCircle class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold text-red-600">{{ syncStats.failed }}</div>
          <p class="text-xs text-muted-foreground">同步失败</p>
        </CardContent>
      </Card>
    </div>

    <div class="grid gap-6 lg:grid-cols-3">
      <div class="lg:col-span-2">
        <Card>
          <CardHeader>
            <CardTitle>同步任务列表</CardTitle>
            <CardDescription>当前系统的数据同步任务</CardDescription>
          </CardHeader>
          <CardContent>
            <div class="space-y-4">
              <div v-for="job in syncJobs" :key="job.id" class="flex items-center justify-between p-4 border rounded-lg">
                <div class="flex items-center space-x-4">
                  <div :class="getStatusColor(job.status)" class="p-2 rounded-lg">
                    <component :is="getStatusIcon(job.status)" class="h-4 w-4 text-white" />
                  </div>
                  <div>
                    <h4 class="font-medium">{{ job.name }}</h4>
                    <p class="text-sm text-muted-foreground">{{ job.description }}</p>
                    <p class="text-xs text-muted-foreground">最后运行: {{ job.lastRun }}</p>
                  </div>
                </div>
                <div class="flex items-center space-x-2">
                  <Badge :variant="getBadgeVariant(job.status)">{{ job.status }}</Badge>
                  <Button variant="ghost" size="sm" @click="runSyncJob(job)">
                    <Play class="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div class="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>快速操作</CardTitle>
          </CardHeader>
          <CardContent class="space-y-2">
            <Button variant="outline" class="w-full justify-start" @click="syncAllData">
              <Database class="mr-2 h-4 w-4" />
              全量同步
            </Button>
            <Button variant="outline" class="w-full justify-start" @click="syncUserData">
              <Users class="mr-2 h-4 w-4" />
              同步用户数据
            </Button>
            <Button variant="outline" class="w-full justify-start" @click="syncSystemData">
              <Settings class="mr-2 h-4 w-4" />
              同步系统数据
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>同步状态</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="space-y-3">
              <div class="flex items-center justify-between">
                <span class="text-sm">数据库连接</span>
                <Badge variant="default">正常</Badge>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm">同步服务</span>
                <Badge variant="default">运行中</Badge>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm">下次自动同步</span>
                <span class="text-xs text-muted-foreground">2小时后</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { 
  RefreshCw, 
  Play, 
  Database, 
  CheckCircle, 
  XCircle, 
  Users, 
  Settings, 
  Clock,
  Pause
} from 'lucide-vue-next'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'

const loading = ref(false)

const syncJobs = ref([
  {
    id: '1',
    name: '用户数据同步',
    description: '同步用户信息和权限',
    status: '运行中',
    lastRun: '5分钟前'
  },
  {
    id: '2',
    name: '系统配置同步',
    description: '同步系统设置和配置信息',
    status: '已完成',
    lastRun: '1小时前'
  },
  {
    id: '3',
    name: '日志数据同步',
    description: '同步系统操作日志',
    status: '失败',
    lastRun: '2小时前'
  }
])

const syncStats = computed(() => ({
  total: syncJobs.value.length,
  running: syncJobs.value.filter(j => j.status === '运行中').length,
  completed: syncJobs.value.filter(j => j.status === '已完成').length,
  failed: syncJobs.value.filter(j => j.status === '失败').length
}))

const getStatusColor = (status: string) => {
  switch (status) {
    case '运行中': return 'bg-blue-500'
    case '已完成': return 'bg-green-500'
    case '失败': return 'bg-red-500'
    case '暂停': return 'bg-gray-500'
    default: return 'bg-gray-500'
  }
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case '运行中': return Play
    case '已完成': return CheckCircle
    case '失败': return XCircle
    case '暂停': return Pause
    default: return Clock
  }
}

const getBadgeVariant = (status: string) => {
  switch (status) {
    case '运行中': return 'secondary'
    case '已完成': return 'default'
    case '失败': return 'destructive'
    default: return 'outline'
  }
}

const refreshSyncJobs = () => {
  loading.value = true
  setTimeout(() => loading.value = false, 1000)
}

const startSync = () => console.log('开始同步')
const runSyncJob = (job: any) => console.log('运行同步任务:', job.name)
const syncAllData = () => console.log('全量同步')
const syncUserData = () => console.log('同步用户数据')
const syncSystemData = () => console.log('同步系统数据')
</script>
