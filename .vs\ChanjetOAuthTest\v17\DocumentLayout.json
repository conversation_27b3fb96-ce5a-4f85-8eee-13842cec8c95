{"Version": 1, "WorkspaceRootPath": "F:\\modules\\开发\\ChanjetOAuthTest\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{C9C659B3-4E93-4382-A8CB-0F0E79B5FC96}|ChanjetOAuthTest.csproj|f:\\modules\\开发\\chanjetoauthtest\\app\\controllers\\oauthcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C9C659B3-4E93-4382-A8CB-0F0E79B5FC96}|ChanjetOAuthTest.csproj|solutionrelative:app\\controllers\\oauthcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C9C659B3-4E93-4382-A8CB-0F0E79B5FC96}|ChanjetOAuthTest.csproj|f:\\modules\\开发\\chanjetoauthtest\\app\\configuration\\chanjetconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C9C659B3-4E93-4382-A8CB-0F0E79B5FC96}|ChanjetOAuthTest.csproj|solutionrelative:app\\configuration\\chanjetconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C9C659B3-4E93-4382-A8CB-0F0E79B5FC96}|ChanjetOAuthTest.csproj|f:\\modules\\开发\\chanjetoauthtest\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{C9C659B3-4E93-4382-A8CB-0F0E79B5FC96}|ChanjetOAuthTest.csproj|solutionrelative:appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{C9C659B3-4E93-4382-A8CB-0F0E79B5FC96}|ChanjetOAuthTest.csproj|f:\\modules\\开发\\chanjetoauthtest\\frontend-app\\src\\views\\oauth.vue||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{C9C659B3-4E93-4382-A8CB-0F0E79B5FC96}|ChanjetOAuthTest.csproj|solutionrelative:frontend-app\\src\\views\\oauth.vue||{40D31677-CBC0-4297-A9EF-89D907823A98}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 63, "SelectedChildIndex": 2, "Children": [{"$type": "Document", "DocumentIndex": 2, "Title": "appsettings.json", "DocumentMoniker": "F:\\modules\\开发\\ChanjetOAuthTest\\appsettings.json", "RelativeDocumentMoniker": "appsettings.json", "ToolTip": "F:\\modules\\开发\\ChanjetOAuthTest\\appsettings.json", "RelativeToolTip": "appsettings.json", "ViewState": "AgIAAEIAAAAAAAAAAAAAABQAAAALAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-08-05T02:42:20.513Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "ChanjetConfig.cs", "DocumentMoniker": "F:\\modules\\开发\\ChanjetOAuthTest\\App\\Configuration\\ChanjetConfig.cs", "RelativeDocumentMoniker": "App\\Configuration\\ChanjetConfig.cs", "ToolTip": "F:\\modules\\开发\\ChanjetOAuthTest\\App\\Configuration\\ChanjetConfig.cs", "RelativeToolTip": "App\\Configuration\\ChanjetConfig.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAuwAkAAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-05T02:41:08.546Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "OAuthController.cs", "DocumentMoniker": "F:\\modules\\开发\\ChanjetOAuthTest\\App\\Controllers\\OAuthController.cs", "RelativeDocumentMoniker": "App\\Controllers\\OAuthController.cs", "ToolTip": "F:\\modules\\开发\\ChanjetOAuthTest\\App\\Controllers\\OAuthController.cs", "RelativeToolTip": "App\\Controllers\\OAuthController.cs", "ViewState": "AgIAAA8AAAAAAAAAAAAAACwAAAA4AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-05T02:38:04.016Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "OAuth.vue", "DocumentMoniker": "F:\\modules\\开发\\ChanjetOAuthTest\\frontend-app\\src\\views\\OAuth.vue", "RelativeDocumentMoniker": "frontend-app\\src\\views\\OAuth.vue", "ToolTip": "F:\\modules\\开发\\ChanjetOAuthTest\\frontend-app\\src\\views\\OAuth.vue", "RelativeToolTip": "frontend-app\\src\\views\\OAuth.vue", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003491|", "WhenOpened": "2025-08-05T02:36:11.826Z", "EditorCaption": ""}, {"$type": "Bookmark", "Name": "ST:128:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:128:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:129:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:130:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:131:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:129:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:130:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}]}]}]}