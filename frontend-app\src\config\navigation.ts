import { 
  LayoutDashboard, 
  Key, 
  Activity, 
  Database, 
  BarChart3, 
  Settings,
  type LucideIcon
} from 'lucide-vue-next'

export interface NavigationItem {
  title: string
  href: string
  icon: LucideIcon
  description: string
}

export const mainNavigation: NavigationItem[] = [
  {
    title: '仪表板',
    href: '/',
    icon: LayoutDashboard,
    description: '系统概览和统计信息'
  },
  {
    title: 'OAuth管理',
    href: '/oauth',
    icon: Key,
    description: 'OAuth应用管理和授权配置'
  },
  {
    title: '令牌管理',
    href: '/tokens',
    icon: Activity,
    description: '访问令牌管理和监控'
  },
  {
    title: '数据同步',
    href: '/sync',
    icon: Database,
    description: '数据同步任务管理'
  }
]

export const systemNavigation: NavigationItem[] = [
  {
    title: '监控中心',
    href: '/monitoring',
    icon: BarChart3,
    description: '系统性能和健康监控'
  },
  {
    title: '系统设置',
    href: '/settings',
    icon: Settings,
    description: '系统配置和用户管理'
  }
]

export const getNavigationItemByPath = (path: string): NavigationItem | undefined => {
  return mainNavigation.find(item => item.href === path) || systemNavigation.find(item => item.href === path)
} 
