using System;
using System.IO;
using Microsoft.Extensions.Logging;

namespace ChanjetOAuthTest.App.Common
{
    /// <summary>
    /// 日志辅助类，提供日志记录功能
    /// </summary>
    public static class LogHelper
    {
        private static readonly object _apiLogLock = new object();
        private static readonly object _fileLogLock = new object();
        private static readonly string _apiLogPath = Path.Combine(AppContext.BaseDirectory, "Logs", "api_logs");
        private static readonly string _fileLogPath = Path.Combine(AppContext.BaseDirectory, "Logs", "file_logs");

        /// <summary>
        /// 初始化日志目录
        /// </summary>
        public static void InitializeLogDirectories()
        {
            Directory.CreateDirectory(_apiLogPath);
            Directory.CreateDirectory(_fileLogPath);
        }

        /// <summary>
        /// 记录API相关日志
        /// </summary>
        /// <param name="message">日志消息</param>
        public static void LogApiMessage(string message)
        {
            lock (_apiLogLock)
            {
                // 控制台输出
                Console.ForegroundColor = ConsoleColor.Green;
                Console.WriteLine($"[API] {DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}");
                Console.ResetColor();

                // 文件输出
                try
                {
                    string logFile = Path.Combine(_apiLogPath, $"api_{DateTime.Now:yyyy-MM-dd}.log");
                    File.AppendAllText(logFile, $"[API] {DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}{Environment.NewLine}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"写入API日志文件失败: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 记录文件同步相关日志
        /// </summary>
        /// <param name="message">日志消息</param>
        public static void LogFileMessage(string message)
        {
            lock (_fileLogLock)
            {
                // 控制台输出
                Console.ForegroundColor = ConsoleColor.Yellow;
                Console.WriteLine($"[文件] {DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}");
                Console.ResetColor();

                // 文件输出
                try
                {
                    string logFile = Path.Combine(_fileLogPath, $"file_{DateTime.Now:yyyy-MM-dd}.log");
                    File.AppendAllText(logFile, $"[文件] {DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}{Environment.NewLine}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"写入文件日志失败: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 记录同步任务日志
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="level">日志级别</param>
        public static void LogSyncMessage(string message, LogLevel level = LogLevel.Information)
        {
            lock (_apiLogLock)
            {
                ConsoleColor color = level switch
                {
                    LogLevel.Error => ConsoleColor.Red,
                    LogLevel.Warning => ConsoleColor.Yellow,
                    LogLevel.Information => ConsoleColor.Cyan,
                    LogLevel.Debug => ConsoleColor.Gray,
                    _ => ConsoleColor.White
                };

                Console.ForegroundColor = color;
                Console.WriteLine($"[同步] {DateTime.Now:yyyy-MM-dd HH:mm:ss} [{level}] - {message}");
                Console.ResetColor();

                // 写入同步日志文件
                try
                {
                    string logFile = Path.Combine(_apiLogPath, $"sync_{DateTime.Now:yyyy-MM-dd}.log");
                    File.AppendAllText(logFile, $"[同步] {DateTime.Now:yyyy-MM-dd HH:mm:ss} [{level}] - {message}{Environment.NewLine}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"写入同步日志文件失败: {ex.Message}");
                }
            }
        }
    }
} 