<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold tracking-tight">Token管理</h1>
        <p class="text-muted-foreground">管理和监控OAuth访问令牌的状态和生命周期</p>
      </div>
      <div class="flex gap-2">
        <Button @click="refreshData" :disabled="loading" variant="outline">
          <RefreshCw class="mr-2 h-4 w-4" :class="{ 'animate-spin': loading }" />
          {{ loading ? '刷新中...' : '刷新数据' }}
        </Button>
        <Button @click="loadTokenStatus" :disabled="loading">
          <Activity class="mr-2 h-4 w-4" />
          更新状态
        </Button>
      </div>
    </div>

    <!-- Token 获取和用户选择 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <Card class="border-green-200 bg-green-50/50">
        <CardHeader>
          <div class="flex items-center space-x-3">
            <div class="bg-green-100 p-3 rounded-lg">
              <Key class="h-6 w-6 text-green-600" />
            </div>
            <div>
              <CardTitle class="text-lg">获取Token</CardTitle>
              <CardDescription>输入授权码获取6天Token</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="space-y-2">
            <label class="text-sm font-medium">授权码*</label>
            <input v-model="authCode" type="text" placeholder="10分钟内有效，仅能使用一次"
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent" />
          </div>
          <Button @click="getTokenByAuthCode" :disabled="!authCode.trim() || loading"
            class="w-full bg-green-600 hover:bg-green-700">
            <Download class="mr-2 h-4 w-4" />
            获取Token
          </Button>
          <p class="text-xs text-muted-foreground">
            获取后Token有效6天，系统会自动在过期前刷新
          </p>
        </CardContent>
      </Card>

      <Card class="border-blue-200 bg-blue-50/50">
        <CardHeader>
          <div class="flex items-center space-x-3">
            <div class="bg-blue-100 p-3 rounded-lg">
              <Settings class="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <CardTitle class="text-lg">用户选择</CardTitle>
              <CardDescription>选择要管理的用户</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent class="space-y-4">
          <select v-model="selectedUserId" @change="loadTokenStatus"
            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            <option v-if="clientCredentials.length === 0" value="default">加载中...</option>
            <option v-for="client in clientCredentials" :key="client.clientId" :value="client.clientId">
              {{ client.clientName }}
            </option>
          </select>
          <Button @click="loadTokenStatus" :disabled="loading" variant="outline" class="w-full">
            <RefreshCw class="mr-2 h-4 w-4" :class="{ 'animate-spin': loading }" />
            {{ loading ? '加载中...' : '刷新状态' }}
          </Button>
        </CardContent>
      </Card>
    </div>

    <!-- Token状态概览卡�?-->
    <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">访问令牌</CardTitle>
          <Key class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold" :class="tokenStatus.accessToken ? 'text-green-600' : 'text-red-600'">
            {{ tokenStatus.accessToken ? '有效' : '无效' }}
          </div>
          <div class="flex items-center text-xs text-muted-foreground mt-2">
            <div class="w-2 h-2 rounded-full mr-2" :class="tokenStatus.accessToken ? 'bg-green-500' : 'bg-red-500'">
            </div>
            Access Token
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">刷新令牌</CardTitle>
          <RefreshCw class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold" :class="tokenStatus.refreshToken ? 'text-green-600' : 'text-red-600'">
            {{ tokenStatus.refreshToken ? '有效' : '无效' }}
          </div>
          <div class="flex items-center text-xs text-muted-foreground mt-2">
            <div class="w-2 h-2 rounded-full mr-2" :class="tokenStatus.refreshToken ? 'bg-green-500' : 'bg-red-500'">
            </div>
            Refresh Token
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">过期时间</CardTitle>
          <Clock class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold" :class="tokenStatus.isExpired ? 'text-red-600' : 'text-green-600'">
            {{ tokenStatus.expiresIn || '未知' }}
          </div>
          <div class="flex items-center text-xs text-muted-foreground mt-2">
            <div class="w-2 h-2 rounded-full mr-2" :class="tokenStatus.isExpired ? 'bg-red-500' : 'bg-green-500'"></div>
            {{ tokenStatus.isExpired ? '已过期' : '剩余时间' }}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">系统健康</CardTitle>
          <Monitor class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ stats.systemHealth }}%</div>
          <Progress :value="stats.systemHealth" class="mt-2" />
        </CardContent>
      </Card>
    </div>

    <!-- Token详细信息 -->
    <Card>
      <CardHeader>
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <div class="bg-gray-100 p-3 rounded-lg">
              <Database class="h-6 w-6 text-gray-600" />
            </div>
            <div>
              <CardTitle class="text-lg">Token详细信息</CardTitle>
              <CardDescription>完整的Token数据和元信息</CardDescription>
            </div>
          </div>
          <div class="flex space-x-2">
            <Button @click="copyTokenToClipboard" :disabled="!tokenStatus.accessToken" variant="outline" size="sm">
              <Upload class="mr-2 h-4 w-4" />
              复制Token
            </Button>
            <Button @click="refreshToken" :disabled="!tokenStatus.refreshToken || loading" size="sm">
              <RefreshCw class="mr-2 h-4 w-4" :class="{ 'animate-spin': loading }" />
              刷新Token
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent class="space-y-6">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">访问令牌 (Access Token)</label>
            <div class="relative">
              <textarea :value="tokenData.access_token || '暂无数据'" readonly
                class="w-full h-32 px-4 py-3 text-sm bg-gray-50 border border-gray-300 rounded-lg font-mono resize-none"></textarea>
              <div class="absolute top-2 right-2">
                <Badge :variant="tokenStatus.accessToken ? 'default' : 'destructive'">
                  {{ tokenStatus.accessToken ? '有效' : '无效' }}
                </Badge>
              </div>
            </div>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">刷新令牌 (Refresh Token)</label>
            <div class="relative">
              <textarea :value="tokenData.refresh_token || '暂无数据'" readonly
                class="w-full h-32 px-4 py-3 text-sm bg-gray-50 border border-gray-300 rounded-lg font-mono resize-none"></textarea>
              <div class="absolute top-2 right-2">
                <Badge :variant="tokenStatus.refreshToken ? 'default' : 'destructive'">
                  {{ tokenStatus.refreshToken ? '有效' : '无效' }}
                </Badge>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4" v-if="tokenData.user_auth_permanent_code">
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center">
              <div class="bg-yellow-100 p-2 rounded-lg mr-3">
                <Key class="h-5 w-5 text-yellow-600" />
              </div>
              <div>
                <h4 class="text-sm font-semibold text-yellow-800">永久授权</h4>
                <p class="text-xs text-yellow-700">可用于后续无需用户重复授权直接获取新Token</p>
              </div>
            </div>
            <Button @click="copyPermanentCode" size="sm" variant="outline">
              <Upload class="mr-1 h-3 w-3" />
              复制
            </Button>
          </div>
          <div class="bg-yellow-100 rounded p-3 font-mono text-sm break-all">
            {{ tokenData.user_auth_permanent_code }}
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Token操作和授权说�?-->
    <div class="grid gap-6 lg:grid-cols-2">
      <!-- Token操作 -->
      <Card>
        <CardHeader>
          <CardTitle class="flex items-center">
            <Zap class="mr-2 h-5 w-5 text-yellow-500" />
            Token操作
          </CardTitle>
          <CardDescription>执行Token相关的管理操作</CardDescription>
        </CardHeader>
        <CardContent class="grid grid-cols-2 gap-3">
          <Button @click="validateToken" :disabled="!tokenStatus.accessToken || loading" variant="outline">
            <Activity class="mr-2 h-4 w-4" />
            验证Token
          </Button>
          <Button @click="revokeToken" :disabled="!tokenStatus.accessToken || loading" variant="destructive">
            <Settings class="mr-2 h-4 w-4" />
            撤销Token
          </Button>
          <Button @click="exportToken" :disabled="!tokenStatus.accessToken" variant="secondary">
            <Download class="mr-2 h-4 w-4" />
            导出Token
          </Button>
          <Button @click="clearToken" variant="outline">
            <Upload class="mr-2 h-4 w-4" />
            清空缓存
          </Button>
        </CardContent>
      </Card>

      <!-- 授权码获取说�?-->
      <Card class="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
        <CardHeader>
          <CardTitle class="flex items-center text-blue-900">
            <Database class="mr-2 h-5 w-5 text-blue-600" />
            授权码获取说�?
          </CardTitle>
          <CardDescription class="text-blue-600">如何获取授权码来获取Token</CardDescription>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="bg-white rounded-lg p-3">
            <h4 class="font-semibold text-gray-900 mb-2 text-sm">授权地址模板:</h4>
            <div class="bg-gray-100 rounded p-2 font-mono text-xs break-all relative group">
              <span
                class="text-gray-800">https://market.chanjet.com/user/v2/authorize?appkey=YOUR_APP_KEY&redirect_uri=YOUR_REDIRECT_URI&scope=auth_all&state=test123</span>
              <Button @click="copyAuthUrl" size="sm" variant="ghost"
                class="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity p-1 h-6 w-6">
                <Upload class="h-3 w-3" />
              </Button>
            </div>
          </div>

          <div class="text-xs space-y-2">
            <div>
              <h4 class="font-semibold text-gray-900">操作步骤:</h4>
              <ol class="list-decimal list-inside space-y-1 text-gray-700 pl-2">
                <li>替换授权地址中的参数</li>
                <li>用户访问并同意授权</li>
                <li>从回调URL获取code参数</li>
                <li>使用code获取Token</li>
              </ol>
            </div>
            <div>
              <h4 class="font-semibold text-gray-900">重要提示:</h4>
              <ul class="list-disc list-inside space-y-1 text-gray-700 pl-2">
                <li>授权码有效期10分钟</li>
                <li>Token有效6天</li>
                <li>请保存永久授权码</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  Key,
  Activity,
  RefreshCw,
  Monitor,
  Clock,
  Database,
  Settings,
  Download,
  Upload,
  Zap
} from 'lucide-vue-next'

// UI 组件
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'

const router = useRouter()
const loading = ref(false)

// Token 管理相关数据
const authCode = ref('')
const selectedUserId = ref('default')
const clientCredentials = ref<Array<{ clientId: string, clientName: string }>>([])

// Token 数据
const tokenData = reactive({
  access_token: '',
  refresh_token: '',
  token_type: '',
  expires_in: '',
  scope: '',
  created_at: '',
  user_auth_permanent_code: ''
})

// Token 状�?
const tokenStatus = reactive({
  accessToken: false,
  refreshToken: false,
  isExpired: false,
  expiresIn: ''
})

// 统计数据
const stats = ref({
  systemHealth: 98
})

// Token管理方法
const loadClientCredentials = async () => {
  try {
    const response = await fetch('/api/external/token/client-credentials')
    const result = await response.json()

    if (result.success && result.data) {
      clientCredentials.value = result.data
      if (clientCredentials.value.length > 0) {
        const currentUserExists = clientCredentials.value.some(c => c.clientId === selectedUserId.value)
        if (!currentUserExists) {
          selectedUserId.value = clientCredentials.value[0].clientId
        }
      }
    } else {
      clientCredentials.value = [
        { clientId: 'default', clientName: '默认用户' },
        { clientId: 'admin', clientName: '管理员' }
      ]
    }
  } catch (error) {
    console.error('加载客户端凭据失�?', error)
    clientCredentials.value = [
      { clientId: 'default', clientName: '默认用户' },
      { clientId: 'admin', clientName: '管理员' }
    ]
  }
}

const getTokenByAuthCode = async () => {
  if (!authCode.value.trim()) {
    alert('请输入有效的授权码')
    return
  }

  loading.value = true

  try {
    const url = `/api/token/get-token?authCode=${encodeURIComponent(authCode.value)}${selectedUserId.value ? '&userId=' + encodeURIComponent(selectedUserId.value) : ''}`
    const response = await fetch(url, { method: 'POST' })
    const result = await response.json()

    if (result.success) {
      if (result.data) {
        Object.assign(tokenData, {
          access_token: result.data.AccessToken || result.data.access_token,
          refresh_token: result.data.RefreshToken || result.data.refresh_token,
          token_type: result.data.TokenType || result.data.token_type || 'Bearer',
          expires_in: result.data.ExpiresIn || result.data.expires_in,
          scope: result.data.Scope || result.data.scope,
          created_at: new Date().toISOString(),
          user_auth_permanent_code: result.data.UserAuthPermanentCode || result.data.user_auth_permanent_code
        })
        updateTokenStatus(tokenData)
      }
      alert('6天有效期Token获取成功')
      authCode.value = ''
    } else {
      let errorMsg = result.message || '获取Token失败'
      if (result.error_code === 'INVALID_AUTH_CODE') {
        errorMsg = '授权码无效或已过期（10分钟有效期），请重新获取授权码'
      }
      alert(errorMsg)
    }
  } catch (error) {
    alert('无法连接到服务器，请检查服务是否正常运行')
  } finally {
    loading.value = false
  }
}

const loadTokenStatus = async () => {
  loading.value = true

  try {
    const response = await fetch(`/api/token/status?userId=${selectedUserId.value}`)
    const result = await response.json()

    if (result.success && result.data) {
      if (result.data.has_token && result.data.token_info) {
        const tokenInfo = result.data.token_info

        try {
          const tokenResponse = await fetch(`/api/token/info?userId=${selectedUserId.value}`)
          const tokenResult = await tokenResponse.json()

          if (tokenResult.success && tokenResult.data) {
            Object.assign(tokenData, {
              access_token: tokenResult.data.access_token || '',
              refresh_token: tokenResult.data.refresh_token || '',
              token_type: tokenResult.data.token_type || 'Bearer',
              expires_in: tokenResult.data.expires_in || '',
              refresh_expires_in: tokenResult.data.refresh_expires_in || '',
              scope: tokenResult.data.scope || '',
              expires_at: tokenResult.data.expires_at || '',
              refresh_expires_at: tokenResult.data.refresh_expires_at || '',
              created_at: tokenResult.data.created_at || tokenInfo.created_at || new Date().toISOString(),
              user_auth_permanent_code: tokenResult.data.user_auth_permanent_code || tokenData.user_auth_permanent_code || ''
            })

            updateTokenStatus(tokenData)
          } else {
            updateTokenStatusFromApiResponse(result.data, tokenInfo)
          }
        } catch (tokenError) {
          console.warn('获取完整token信息失败:', tokenError)
          updateTokenStatusFromApiResponse(result.data, tokenInfo)
        }
      } else {
        resetTokenData()
      }
    } else {
      resetTokenData()
    }
  } catch (error) {
    console.error('获取Token状态失败', error)
    resetTokenData()
  } finally {
    loading.value = false
  }
}

const updateTokenStatusFromApiResponse = (statusData: any, tokenInfo: any) => {
  tokenStatus.accessToken = statusData.has_token && statusData.is_valid
  tokenStatus.refreshToken = statusData.has_token

  if (tokenInfo) {
    const expiresAt = new Date(tokenInfo.expires_at).getTime()
    const now = new Date().getTime()
    tokenStatus.isExpired = now > expiresAt

    if (!tokenStatus.isExpired) {
      const remainingMinutes = Math.floor((expiresAt - now) / (1000 * 60))
      const hours = Math.floor(remainingMinutes / 60)
      const minutes = remainingMinutes % 60
      tokenStatus.expiresIn = `${hours}小时${minutes}分钟`
    } else {
      tokenStatus.expiresIn = '已过期'
    }

    Object.assign(tokenData, {
      access_token: tokenStatus.accessToken ? '****有效token****' : '',
      refresh_token: tokenStatus.refreshToken ? '****有效refresh token****' : '',
      token_type: 'Bearer',
      expires_in: tokenInfo.time_to_expiry ? Math.floor(tokenInfo.time_to_expiry * 60) : '',
      scope: 'auth_all',
      created_at: tokenInfo.created_at || ''
    })
  } else {
    tokenStatus.expiresIn = '未知'
  }
}

const updateTokenStatus = (data: any) => {
  tokenStatus.accessToken = !!data.access_token
  tokenStatus.refreshToken = !!data.refresh_token

  if (data.expires_in && data.created_at) {
    const expiresAt = new Date(data.created_at).getTime() + (data.expires_in * 1000)
    const now = new Date().getTime()
    tokenStatus.isExpired = now > expiresAt

    if (!tokenStatus.isExpired) {
      const remainingSeconds = Math.floor((expiresAt - now) / 1000)
      const hours = Math.floor(remainingSeconds / 3600)
      const minutes = Math.floor((remainingSeconds % 3600) / 60)
      tokenStatus.expiresIn = `${hours}小时${minutes}分钟`
    } else {
      tokenStatus.expiresIn = '已过期'
    }
  } else {
    tokenStatus.expiresIn = '未知'
  }
}

const resetTokenData = () => {
  Object.assign(tokenData, {
    access_token: '',
    refresh_token: '',
    token_type: '',
    expires_in: '',
    scope: '',
    created_at: '',
    user_auth_permanent_code: ''
  })

  Object.assign(tokenStatus, {
    accessToken: false,
    refreshToken: false,
    isExpired: false,
    expiresIn: ''
  })
}

const refreshToken = async () => {
  loading.value = true

  try {
    const response = await fetch('/api/token/refresh', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ userId: selectedUserId.value })
    })

    const result = await response.json()

    if (result.success) {
      Object.assign(tokenData, result.data)
      updateTokenStatus(result.data)
      alert('Token已成功刷新')
    } else {
      alert(result.message || 'Token刷新失败')
    }
  } catch (error) {
    alert('无法连接到服务器')
  } finally {
    loading.value = false
  }
}

const validateToken = async () => {
  loading.value = true

  try {
    const response = await fetch('/api/token/validate', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ userId: selectedUserId.value })
    })

    const result = await response.json()

    if (result.success) {
      alert('Token有效且可使用')
    } else {
      alert(result.message || 'Token无效或已过期')
    }
  } catch (error) {
    alert('无法连接到服务器')
  } finally {
    loading.value = false
  }
}

const revokeToken = async () => {
  if (!confirm('确定要撤销Token吗？这将使Token立即失效')) {
    return
  }

  loading.value = true

  try {
    const response = await fetch('/api/token/revoke', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ userId: selectedUserId.value })
    })

    const result = await response.json()

    if (result.success) {
      resetTokenData()
      alert('Token已被撤销')
    } else {
      alert(result.message || 'Token撤销失败')
    }
  } catch (error) {
    alert('无法连接到服务器')
  } finally {
    loading.value = false
  }
}

const copyTokenToClipboard = async () => {
  try {
    await navigator.clipboard.writeText(tokenData.access_token)
    alert('Token已复制到剪贴板')
  } catch (error) {
    alert('无法复制到剪贴板')
  }
}

const copyPermanentCode = async () => {
  try {
    await navigator.clipboard.writeText(tokenData.user_auth_permanent_code)
    alert('永久授权码已复制到剪贴板')
  } catch (error) {
    alert('无法复制到剪贴板')
  }
}

const copyAuthUrl = async () => {
  const authUrl = 'https://market.chanjet.com/user/v2/authorize?appkey=YOUR_APP_KEY&redirect_uri=YOUR_REDIRECT_URI&scope=auth_all&state=test123'
  try {
    await navigator.clipboard.writeText(authUrl)
    alert('授权地址已复制，请替换YOUR_APP_KEY等参数')
  } catch (error) {
    alert('无法复制到剪贴板')
  }
}

const exportToken = () => {
  const data = JSON.stringify(tokenData, null, 2)
  const blob = new Blob([data], { type: 'application/json' })
  const url = URL.createObjectURL(blob)

  const a = document.createElement('a')
  a.href = url
  a.download = `token_${selectedUserId.value}_${new Date().toISOString().split('T')[0]}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)

  alert('Token数据已导出')
}

const clearToken = () => {
  if (confirm('确定要清空Token缓存吗？')) {
    resetTokenData()
    alert('Token缓存已清空')
  }
}

// 刷新数据
const refreshData = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadClientCredentials(),
      loadTokenStatus()
    ])
    stats.value.systemHealth = Math.floor(Math.random() * 10) + 90
  } finally {
    loading.value = false
  }
}

onMounted(async () => {
  await loadClientCredentials()
  await loadTokenStatus()
})
</script>
