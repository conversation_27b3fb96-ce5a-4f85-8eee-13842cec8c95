using ChanjetOAuthTest.App.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ChanjetOAuthTest.App.Interfaces
{
    /// <summary>
    /// 数据同步服务接口
    /// </summary>
    public interface IDataSyncService
    {
        /// <summary>
        /// 启动API同步任务
        /// </summary>
        void StartApiSync();

        /// <summary>
        /// 停止API同步任务
        /// </summary>
        void StopApiSync();

        /// <summary>
        /// 执行自定义API调用
        /// </summary>
        /// <param name="accountIndex">账户索引</param>
        /// <param name="endpoint">端点</param>
        /// <param name="requestBody">请求体</param>
        /// <returns>响应结果</returns>
        Task<string> ExecuteCustomApiCall(int accountIndex, string endpoint, string requestBody = "{}");

        /// <summary>
        /// 验证Token是否有效
        /// </summary>
        /// <param name="accountIndex">账户索引</param>
        /// <returns>是否有效</returns>
        Task<bool> ValidateTokenAsync(int accountIndex);

        /// <summary>
        /// 刷新API端点配置
        /// </summary>
        void RefreshApiEndpoints();

        /// <summary>
        /// 获取同步状态
        /// </summary>
        /// <returns>同步状态信息</returns>
        Dictionary<string, object> GetSyncStatus();

        /// <summary>
        /// 获取API端点列表
        /// </summary>
        /// <returns>API端点列表</returns>
        List<ApiEndpoint> GetApiEndpoints();

        /// <summary>
        /// 更新API端点配置
        /// </summary>
        /// <param name="endpoints">端点列表</param>
        /// <returns>是否成功</returns>
        bool UpdateApiEndpoints(List<ApiEndpoint> endpoints);
    }
} 