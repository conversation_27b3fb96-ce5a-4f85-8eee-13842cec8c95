using System.Text.Json.Serialization;

namespace ChanjetOAuthTest.App.Models
{
    public class RefreshTokenResponse
    {
        [JsonPropertyName("access_token")]
        public string AccessToken { get; set; }

        [JsonPropertyName("refresh_token")]
        public string RefreshToken { get; set; }

        [JsonPropertyName("expires_in")]
        public int ExpiresIn { get; set; }

        [JsonPropertyName("refresh_expires_in")]
        public int RefreshExpiresIn { get; set; }

        [JsonPropertyName("token_type")]
        public string TokenType { get; set; }

        [JsonPropertyName("scope")]
        public string Scope { get; set; }

        /// <summary>
        /// 用户永久授权码（用于长期获取token，无需用户重复授权）
        /// </summary>
        [JsonPropertyName("user_auth_permanent_code")]
        public string UserAuthPermanentCode { get; set; }
    }
} 