# 畅捷通OAuth Token管理面板 - 项目发布指南

## 🎯 项目概述
这是一个基于 ASP.NET Core 5.0 + Vue 3 的畅捷通OAuth Token管理面板，提供Token获取、刷新、管理等功能。

## 📦 技术栈
- **后端**: ASP.NET Core 5.0 Web API
- **前端**: Vue 3 + Composition API + Tailwind CSS
- **依赖**: Microsoft.AspNetCore.App 2.2.8
- **端口**: 35010

## 🚀 发布方式

### 方式一：本地/服务器部署

#### 1. 环境准备
```bash
# 安装 .NET 5.0 Runtime
# Windows: 下载 .NET 5.0 Runtime 安装包
# Linux: 
wget https://dot.net/v1/dotnet-install.sh
chmod +x dotnet-install.sh
./dotnet-install.sh --version 5.0.17
```

#### 2. 项目发布
```bash
# 进入项目根目录
cd ChanjetOAuthTest

# 发布项目 (生产模式)
dotnet publish -c Release -o ./publish

# 或者指定运行时 (Windows)
dotnet publish -c Release -r win-x64 --self-contained -o ./publish

# Linux
dotnet publish -c Release -r linux-x64 --self-contained -o ./publish
```

#### 3. 配置修改
发布前需要修改 `appsettings.json` 中的配置：

```json
{
  "Chanjet": {
    "AppKey": "YOUR_ACTUAL_APP_KEY",
    "AppSecret": "YOUR_ACTUAL_APP_SECRET",
    "RedirectUri": "http://your-domain.com:35010/oauth/callback",
    "TestOrgId": "YOUR_ORG_ID"
  }
}
```

#### 4. 启动应用
```bash
# 进入发布目录
cd publish

# 启动应用
dotnet ChanjetOAuthTest.dll

# 或者 (如果是自包含发布)
./ChanjetOAuthTest.exe  # Windows
./ChanjetOAuthTest      # Linux
```

#### 5. 访问地址
- 管理面板: `http://localhost:35010/page/dashboard.html`
- API文档: `http://localhost:35010/api/token/status`

### 方式二：Docker 容器化部署

#### 1. 创建 Dockerfile
```dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:5.0 AS base
WORKDIR /app
EXPOSE 35010

FROM mcr.microsoft.com/dotnet/sdk:5.0 AS build
WORKDIR /src
COPY ["ChanjetOAuthTest.csproj", "."]
RUN dotnet restore "ChanjetOAuthTest.csproj"
COPY . .
WORKDIR "/src"
RUN dotnet build "ChanjetOAuthTest.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "ChanjetOAuthTest.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "ChanjetOAuthTest.dll"]
```

#### 2. 构建和运行
```bash
# 构建镜像
docker build -t chanjet-oauth-panel .

# 运行容器
docker run -d -p 35010:35010 --name chanjet-oauth chanjet-oauth-panel

# 或者使用 docker-compose
```

#### 3. Docker Compose
```yaml
version: '3.8'
services:
  chanjet-oauth:
    build: .
    ports:
      - "35010:35010"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
    volumes:
      - ./data:/app/data  # 持久化数据
    restart: unless-stopped
```

### 方式三：IIS 部署 (Windows)

#### 1. 安装 ASP.NET Core Hosting Bundle
下载并安装 ASP.NET Core 5.0 Hosting Bundle

#### 2. 发布项目
```bash
dotnet publish -c Release -o ./publish
```

#### 3. IIS 配置
1. 打开 IIS 管理器
2. 添加网站
3. 设置物理路径为发布目录
4. 设置端口为 35010
5. 应用程序池设置为 "无托管代码"

### 方式四：云平台部署

#### Azure App Service
1. 创建 App Service (Linux 或 Windows)
2. 配置 .NET 5.0 运行时
3. 部署发布包
4. 配置自定义域名和SSL

#### AWS/腾讯云/阿里云
1. 创建 ECS 实例
2. 安装 .NET 5.0 Runtime
3. 部署应用
4. 配置反向代理 (Nginx)

## 🔧 生产环境配置

### 1. 安全配置
```json
{
  "AllowedHosts": "your-domain.com",
  "Chanjet": {
    "AppKey": "PROD_APP_KEY",
    "AppSecret": "PROD_APP_SECRET",
    "RedirectUri": "https://your-domain.com/oauth/callback"
  }
}
```

### 2. 日志配置
```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Warning",
      "ChanjetOAuth": "Information"
    },
    "File": {
      "Path": "logs/app-{Date}.log",
      "MinLevel": "Information"
    }
  }
}
```

### 3. 性能优化
- 启用 gzip 压缩
- 配置静态文件缓存
- 使用 CDN 加速静态资源

## 🛡️ 反向代理配置 (推荐)

### Nginx 配置
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:35010;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection keep-alive;
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 📊 监控和维护

### 1. 健康检查
访问: `http://your-domain.com:35010/api/token/status`

### 2. 日志监控
- 应用日志路径: `logs/`
- 系统日志: Event Viewer (Windows) 或 journalctl (Linux)

### 3. 性能监控
- CPU 和内存使用率
- 请求响应时间
- API 调用成功率

## 🔐 安全注意事项

1. **API 密钥安全**
   - 不要在代码中硬编码 AppKey 和 AppSecret
   - 使用环境变量或安全的配置管理

2. **HTTPS 配置**
   - 生产环境强制使用 HTTPS
   - 配置 SSL 证书

3. **访问控制**
   - 限制管理面板访问IP
   - 添加身份验证

4. **数据安全**
   - 定期备份 Token 数据
   - 加密敏感信息

## 📞 故障排除

### 常见问题
1. **端口占用**: 检查 35010 端口是否被占用
2. **权限问题**: 确保应用有足够的文件系统权限
3. **依赖缺失**: 安装完整的 .NET 5.0 Runtime

### 调试命令
```bash
# 检查端口占用
netstat -tulpn | grep 35010

# 查看应用日志
tail -f logs/app-*.log

# 检查进程状态
ps aux | grep ChanjetOAuthTest
```

## 🎉 发布完成

项目成功发布后，你可以：
1. 访问管理面板进行 Token 管理
2. 使用 API 接口集成到其他系统
3. 监控应用运行状态
4. 根据需要进行扩展和优化

---

**注意**: 发布前请确保所有配置正确，特别是 AppKey、AppSecret 和回调地址等关键配置。 