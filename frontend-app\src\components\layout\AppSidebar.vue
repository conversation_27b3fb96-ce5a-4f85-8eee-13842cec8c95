<script setup lang="ts">
import { ShieldCheck } from 'lucide-vue-next'

// 配置和工具函数
import { mainNavigation } from '@/config/navigation'

// Sidebar 组件
import {
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton
} from '@/components/ui/sidebar'
</script>

<template>
  <Sidebar class="border-r border-slate-200/60 bg-white/95 backdrop-blur-xl shadow-lg shadow-slate-900/5">
    <!-- 侧边栏头部 -->
    <SidebarHeader class="border-b border-slate-200/60 bg-gradient-to-r from-blue-600/5 to-indigo-600/5 p-6">
      <div class="flex items-center space-x-3">
        <div class="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-blue-600 to-indigo-600 shadow-lg shadow-blue-500/25">
          <ShieldCheck class="h-5 w-5 text-white" />
        </div>
        <div class="flex flex-col">
          <h1 class="text-lg font-bold text-slate-800">OAuth控制台</h1>
          <p class="text-xs text-slate-500">授权管理系统</p>
        </div>
      </div>
    </SidebarHeader>

    <!-- 侧边栏内容 -->
    <SidebarContent class="p-4">
      <SidebarMenu class="space-y-2">
        <SidebarMenuItem v-for="item in mainNavigation" :key="item.title">
          <router-link 
            :to="item.href" 
            class="block w-full"
            v-slot="{ isActive }"
          >
            <SidebarMenuButton
              :class="{
                'bg-gradient-to-r from-blue-500 to-indigo-500 text-white shadow-lg shadow-blue-500/25': isActive,
                'text-slate-600 hover:text-slate-800 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50': !isActive
              }"
              class="w-full justify-start rounded-xl px-4 py-3 font-medium transition-all duration-200 hover:shadow-sm group"
              :title="item.description"
            >
              <component 
                :is="item.icon" 
                class="mr-3 h-5 w-5 transition-colors"
                :class="isActive ? 'text-white' : 'text-slate-400 group-hover:text-slate-600'"
              />
              {{ item.title }}
            </SidebarMenuButton>
          </router-link>
        </SidebarMenuItem>
      </SidebarMenu>
    </SidebarContent>
  </Sidebar>
</template> 
