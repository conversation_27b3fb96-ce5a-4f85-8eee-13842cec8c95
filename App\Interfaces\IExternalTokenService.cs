using ChanjetOAuthTest.App.Models;
using ChanjetOAuthTest.App.Configuration;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ChanjetOAuthTest.App.Interfaces
{
    /// <summary>
    /// 外部Token服务接口，为外部系统提供Token管理功能
    /// </summary>
    public interface IExternalTokenService
    {
        /// <summary>
        /// 获取Token（外部接口）
        /// </summary>
        /// <param name="request">Token获取请求</param>
        /// <param name="clientInfo">客户端信息（IP、UserAgent等）</param>
        /// <returns>Token获取响应</returns>
        Task<ExternalTokenAcquireResponse> AcquireTokenAsync(ExternalTokenRequest request, ClientInfo clientInfo);

        /// <summary>
        /// 刷新Token（外部接口）
        /// </summary>
        /// <param name="request">Token刷新请求</param>
        /// <param name="clientInfo">客户端信息</param>
        /// <returns>Token获取响应</returns>
        Task<ExternalTokenAcquireResponse> RefreshTokenAsync(ExternalTokenRefreshRequest request, ClientInfo clientInfo);

        /// <summary>
        /// 验证Token（外部接口）
        /// </summary>
        /// <param name="request">Token验证请求</param>
        /// <param name="clientInfo">客户端信息</param>
        /// <returns>Token验证响应</returns>
        Task<ExternalTokenValidateResponse> ValidateTokenAsync(ExternalTokenValidateRequest request, ClientInfo clientInfo);

        /// <summary>
        /// 撤销Token（外部接口）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="clientId">客户端ID</param>
        /// <param name="clientSecret">客户端密钥</param>
        /// <param name="clientInfo">客户端信息</param>
        /// <returns>操作结果</returns>
        Task<ExternalTokenResponse> RevokeTokenAsync(string userId, string clientId, string clientSecret, ClientInfo clientInfo);

        /// <summary>
        /// 验证客户端凭据
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <param name="clientSecret">客户端密钥</param>
        /// <returns>验证结果</returns>
        Task<bool> ValidateClientCredentialsAsync(string clientId, string clientSecret);

        /// <summary>
        /// 记录Token使用情况
        /// </summary>
        /// <param name="record">使用记录</param>
        /// <returns>记录操作结果</returns>
        Task<bool> RecordTokenUsageAsync(TokenUsageRecord record);

        /// <summary>
        /// 获取Token使用记录
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="clientId">客户端ID（可选）</param>
        /// <param name="startTime">开始时间（可选）</param>
        /// <param name="endTime">结束时间（可选）</param>
        /// <param name="pageNumber">页码</param>
        /// <param name="pageSize">页面大小</param>
        /// <returns>使用记录列表</returns>
        Task<ExternalTokenUsageResponse> GetTokenUsageRecordsAsync(
            string userId,
            string clientId = null,
            DateTime? startTime = null,
            DateTime? endTime = null,
            int pageNumber = 1,
            int pageSize = 20);

        /// <summary>
        /// 获取Token使用统计
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="clientId">客户端ID（可选）</param>
        /// <param name="days">统计天数</param>
        /// <returns>使用统计信息</returns>
        Task<TokenUsageStatistics> GetTokenUsageStatisticsAsync(string userId, string clientId = null, int days = 30);

        /// <summary>
        /// 获取客户端凭据列表
        /// </summary>
        /// <returns>客户端凭据列表</returns>
        Task<List<ClientCredential>> GetClientCredentialsAsync();
    }

    /// <summary>
    /// 客户端信息
    /// </summary>
    public class ClientInfo
    {
        /// <summary>
        /// IP地址
        /// </summary>
        public string IpAddress { get; set; }

        /// <summary>
        /// 用户代理
        /// </summary>
        public string UserAgent { get; set; }

        /// <summary>
        /// 请求ID
        /// </summary>
        public string RequestId { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 请求时间
        /// </summary>
        public DateTime RequestTime { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Token使用统计信息
    /// </summary>
    public class TokenUsageStatistics
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public string UserId { get; set; }

        /// <summary>
        /// 客户端ID
        /// </summary>
        public string ClientId { get; set; }

        /// <summary>
        /// 统计开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 统计结束时间
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// 总请求次数
        /// </summary>
        public int TotalRequests { get; set; }

        /// <summary>
        /// 成功请求次数
        /// </summary>
        public int SuccessfulRequests { get; set; }

        /// <summary>
        /// 失败请求次数
        /// </summary>
        public int FailedRequests { get; set; }

        /// <summary>
        /// 按请求类型分组的统计
        /// </summary>
        public Dictionary<TokenRequestType, int> RequestsByType { get; set; } = new Dictionary<TokenRequestType, int>();

        /// <summary>
        /// 按日期分组的统计
        /// </summary>
        public Dictionary<DateTime, int> RequestsByDate { get; set; } = new Dictionary<DateTime, int>();

        /// <summary>
        /// 成功率
        /// </summary>
        public double SuccessRate => TotalRequests > 0 ? (double)SuccessfulRequests / TotalRequests * 100 : 0;
    }
} 