using ChanjetOAuthTest.App.Models;
using ChanjetOAuthTest.App.Interfaces;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace ChanjetOAuthTest.App.Services
{
    /// <summary>
    /// 基于JSON文件的Token存储实现
    /// </summary>
    public class JsonTokenStorage : ITokenStorage
    {
        private readonly string _filePath;
        private readonly ILogger<JsonTokenStorage> _logger;
        private readonly SemaphoreSlim _fileLock;
        
        // JSON序列化选项
        private static readonly JsonSerializerOptions JsonOptions = new()
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = true,
            Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
        };

        public JsonTokenStorage(ILogger<JsonTokenStorage> logger, string storagePath = null)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _filePath = storagePath ?? Path.Combine(AppContext.BaseDirectory, "Data", "tokens.json");
            _fileLock = new SemaphoreSlim(1, 1);

            // 确保目录存在
            var directory = Path.GetDirectoryName(_filePath);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
                _logger.LogInformation("创建Token存储目录: {Directory}", directory);
            }

            _logger.LogInformation("JsonTokenStorage初始化完成，存储路径: {FilePath}", _filePath);
        }

        /// <summary>
        /// 保存Token信息
        /// </summary>
        public async Task SaveTokenAsync(string userId, TokenInfo tokenInfo)
        {
            if (string.IsNullOrWhiteSpace(userId))
                throw new ArgumentException("用户ID不能为空", nameof(userId));
            
            if (tokenInfo == null)
                throw new ArgumentNullException(nameof(tokenInfo));

            await _fileLock.WaitAsync();
            try
            {
                var tokens = await LoadTokensFromFileAsync();
                tokens[userId] = tokenInfo;
                await SaveTokensToFileAsync(tokens);

                _logger.LogInformation("Token已保存到文件，用户ID: {UserId}, 过期时间: {ExpiresAt}", 
                    userId, tokenInfo.ExpiresAt);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存Token到文件失败，用户ID: {UserId}", userId);
                throw;
            }
            finally
            {
                _fileLock.Release();
            }
        }

        /// <summary>
        /// 获取Token信息
        /// </summary>
        public async Task<TokenInfo> GetTokenAsync(string userId)
        {
            if (string.IsNullOrWhiteSpace(userId))
                throw new ArgumentException("用户ID不能为空", nameof(userId));

            await _fileLock.WaitAsync();
            try
            {
                var tokens = await LoadTokensFromFileAsync();
                tokens.TryGetValue(userId, out var tokenInfo);
                
                if (tokenInfo != null)
                {
                    _logger.LogDebug("从文件加载Token成功，用户ID: {UserId}", userId);
                }
                
                return tokenInfo;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "从文件加载Token失败，用户ID: {UserId}", userId);
                return null;
            }
            finally
            {
                _fileLock.Release();
            }
        }

        /// <summary>
        /// 删除Token信息
        /// </summary>
        public async Task RemoveTokenAsync(string userId)
        {
            if (string.IsNullOrWhiteSpace(userId))
                throw new ArgumentException("用户ID不能为空", nameof(userId));

            await _fileLock.WaitAsync();
            try
            {
                var tokens = await LoadTokensFromFileAsync();
                if (tokens.Remove(userId))
                {
                    await SaveTokensToFileAsync(tokens);
                    _logger.LogInformation("Token已从文件删除，用户ID: {UserId}", userId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除Token文件失败，用户ID: {UserId}", userId);
                throw;
            }
            finally
            {
                _fileLock.Release();
            }
        }

        /// <summary>
        /// 获取所有Token信息
        /// </summary>
        public async Task<Dictionary<string, TokenInfo>> GetAllTokensAsync()
        {
            await _fileLock.WaitAsync();
            try
            {
                return await LoadTokensFromFileAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载所有Token失败");
                return new Dictionary<string, TokenInfo>();
            }
            finally
            {
                _fileLock.Release();
            }
        }

        /// <summary>
        /// 清空所有Token信息
        /// </summary>
        public async Task ClearAllTokensAsync()
        {
            await _fileLock.WaitAsync();
            try
            {
                await SaveTokensToFileAsync(new Dictionary<string, TokenInfo>());
                _logger.LogInformation("所有Token已清空");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清空Token文件失败");
                throw;
            }
            finally
            {
                _fileLock.Release();
            }
        }

        /// <summary>
        /// 从文件加载Token信息
        /// </summary>
        private async Task<Dictionary<string, TokenInfo>> LoadTokensFromFileAsync()
        {
            try
            {
                if (!File.Exists(_filePath))
                {
                    _logger.LogDebug("Token文件不存在，返回空字典: {FilePath}", _filePath);
                    return new Dictionary<string, TokenInfo>();
                }

                var json = await File.ReadAllTextAsync(_filePath);
                if (string.IsNullOrWhiteSpace(json))
                {
                    return new Dictionary<string, TokenInfo>();
                }

                var tokens = JsonSerializer.Deserialize<Dictionary<string, TokenInfo>>(json, JsonOptions) 
                           ?? new Dictionary<string, TokenInfo>();

                _logger.LogDebug("从文件加载Token信息，数量: {Count}", tokens.Count);
                return tokens;
            }
            catch (JsonException ex)
            {
                _logger.LogError(ex, "Token文件格式错误，将创建新文件");
                return new Dictionary<string, TokenInfo>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "读取Token文件失败");
                throw;
            }
        }

        /// <summary>
        /// 保存Token信息到文件
        /// </summary>
        private async Task SaveTokensToFileAsync(Dictionary<string, TokenInfo> tokens)
        {
            try
            {
                var json = JsonSerializer.Serialize(tokens, JsonOptions);
                await File.WriteAllTextAsync(_filePath, json);
                
                _logger.LogDebug("Token信息已保存到文件，数量: {Count}", tokens.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "写入Token文件失败");
                throw;
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _fileLock?.Dispose();
        }
    }
} 