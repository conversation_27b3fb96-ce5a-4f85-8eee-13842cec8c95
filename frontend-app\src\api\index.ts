import axios from 'axios'

// 创建 axios 实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'https://jsonplaceholder.typicode.com',
  timeout: Number(import.meta.env.VITE_API_TIMEOUT) || 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 在发送请求之前做些什么
    console.log('发送请求', config)
    
    // 可以在这里添加认证token
    // const token = localStorage.getItem('token')
    // if (token) {
    //   config.headers.Authorization = `Bearer ${token}`
    // }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    // 对响应数据做点什么
    console.log('收到响应:', response)
    return response
  },
  (error) => {
    // 对响应错误做点什么
    console.error('请求错误:', error)
    
    // 可以在这里处理常见错误
    if (error.response?.status === 401) {
      // 处理未授权错误
      console.log('用户未授权，需要重新登录')
    }
    
    return Promise.reject(error)
  }
)

// API 方法示例
export const apiMethods = {
  // 获取文章列表
  getPosts: () => api.get('/posts'),
  
  // 获取单个文章
  getPost: (id: number) => api.get(`/posts/${id}`),
  
  // 创建文章
  createPost: (data: any) => api.post('/posts', data),
  
  // 更新文章
  updatePost: (id: number, data: any) => api.put(`/posts/${id}`, data),
  
  // 删除文章
  deletePost: (id: number) => api.delete(`/posts/${id}`),
}

export default api 
