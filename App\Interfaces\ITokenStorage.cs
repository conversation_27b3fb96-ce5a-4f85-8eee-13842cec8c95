using ChanjetOAuthTest.App.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ChanjetOAuthTest.App.Interfaces
{
    /// <summary>
    /// Token存储接口，用于持久化Token信息
    /// </summary>
    public interface ITokenStorage
    {
        /// <summary>
        /// 保存Token信息
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="tokenInfo">Token信息</param>
        /// <returns></returns>
        Task SaveTokenAsync(string userId, TokenInfo tokenInfo);

        /// <summary>
        /// 获取Token信息
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>Token信息，不存在则返回null</returns>
        Task<TokenInfo> GetTokenAsync(string userId);

        /// <summary>
        /// 删除Token信息
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns></returns>
        Task RemoveTokenAsync(string userId);

        /// <summary>
        /// 获取所有Token信息
        /// </summary>
        /// <returns>所有Token信息的字典</returns>
        Task<Dictionary<string, TokenInfo>> GetAllTokensAsync();

        /// <summary>
        /// 清空所有Token信息
        /// </summary>
        /// <returns></returns>
        Task ClearAllTokensAsync();
    }
} 