import { ref, computed, onMounted, onUnmounted } from 'vue'

export interface SystemStatusItem {
  id: string
  name: string
  status: 'healthy' | 'warning' | 'error' | 'unknown'
  lastChecked?: Date
  message?: string
}

export interface SystemStatus {
  oauth: SystemStatusItem
  token: SystemStatusItem
  sync: SystemStatusItem
}

export const useSystemStatus = () => {
  const isLoading = ref(false)
  const lastUpdated = ref<Date>()
  const updateInterval = ref<number>()

  const systemStatus = ref<SystemStatus>({
    oauth: {
      id: 'oauth',
      name: 'OAuth服务',
      status: 'healthy',
      lastChecked: new Date(),
      message: 'OAuth服务运行正常'
    },
    token: {
      id: 'token',
      name: 'Token管理',
      status: 'healthy', 
      lastChecked: new Date(),
      message: 'Token管理服务正常'
    },
    sync: {
      id: 'sync',
      name: '数据同步',
      status: 'warning',
      lastChecked: new Date(),
      message: '数据同步服务暂停'
    }
  })

  // 计算整体系统健康状态
  const overallStatus = computed(() => {
    const statuses = Object.values(systemStatus.value)
    
    if (statuses.some(s => s.status === 'error')) {
      return 'error'
    }
    if (statuses.some(s => s.status === 'warning')) {
      return 'warning'
    }
    if (statuses.every(s => s.status === 'healthy')) {
      return 'healthy'
    }
    return 'unknown'
  })

  // 获取状态对应的颜色
  const getStatusColor = (status: SystemStatusItem['status']) => {
    const colors = {
      healthy: 'bg-emerald-500 shadow-emerald-500/25',
      warning: 'bg-amber-500 shadow-amber-500/25', 
      error: 'bg-red-500 shadow-red-500/25',
      unknown: 'bg-slate-400 shadow-slate-400/25'
    }
    return colors[status]
  }

  // 刷新系统状态
  const refreshSystemStatus = async () => {
    isLoading.value = true
    try {
      // 模拟API调用 - 在实际项目中替换为真实的API请求
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 更新状态- 这里可以添加实际的状态检查逻辑
      const now = new Date()
      Object.values(systemStatus.value).forEach(status => {
        status.lastChecked = now
        // 随机模拟状态变化- 实际项目中删除这部分
        if (Math.random() > 0.8) {
          const statuses: SystemStatusItem['status'][] = ['healthy', 'warning', 'error']
          status.status = statuses[Math.floor(Math.random() * statuses.length)]
        }
      })
      
      lastUpdated.value = now
    } catch (error) {
      console.error('刷新系统状态失败', error)
      // 处理错误状态
      Object.values(systemStatus.value).forEach(status => {
        status.status = 'unknown'
        status.message = '状态检查失败'
      })
    } finally {
      isLoading.value = false
    }
  }

  // 开始定期更新
  const startPeriodicUpdate = (intervalMs: number = 30000) => {
    if (updateInterval.value) {
      clearInterval(updateInterval.value)
    }
    updateInterval.value = setInterval(refreshSystemStatus, intervalMs)
  }

  // 停止定期更新
  const stopPeriodicUpdate = () => {
    if (updateInterval.value) {
      clearInterval(updateInterval.value)
      updateInterval.value = undefined
    }
  }

  // 组件挂载时初始化
  onMounted(() => {
    refreshSystemStatus()
    startPeriodicUpdate()
  })

  // 组件卸载时清理
  onUnmounted(() => {
    stopPeriodicUpdate()
  })

  return {
    systemStatus: computed(() => systemStatus.value),
    overallStatus,
    isLoading: computed(() => isLoading.value),
    lastUpdated: computed(() => lastUpdated.value),
    refreshSystemStatus,
    getStatusColor,
    startPeriodicUpdate,
    stopPeriodicUpdate
  }
} 
