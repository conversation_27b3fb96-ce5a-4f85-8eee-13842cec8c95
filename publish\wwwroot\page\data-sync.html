<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据同步管理 - ChanjetOAuth</title>
    <!-- 引入Tailwind CSS -->
    <script src="/src/js/tailwindcss.js"></script>
    <!-- 引入Lucide图标库 -->
    <script src="/src/js/lucide.min.js"></script>
    <!-- 引入Vue.js -->
    <script src="/src/js/vue.global.js"></script>
    <style>
        /* 自定义全局样式和字体 */
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f7fafc;
        }
        
        /* 美化滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        /* 导航栏样式 */
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        /* 脉冲动画 */
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        .animate-pulse {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }

        /* 日志滚动 */
        .log-container {
            scrollbar-width: thin;
            scrollbar-color: #cbd5e0 #f7fafc;
        }
    </style>
</head>
<body class="text-gray-800 bg-gray-100">
    <div id="app">
        <!-- 移动端菜单按钮 -->
        <button 
            @click="toggleSidebar" 
            class="lg:hidden fixed top-4 left-4 z-50 bg-blue-600 text-white p-3 rounded-lg shadow-lg hover:bg-blue-700 transition-colors"
        >
            <i data-lucide="menu" class="w-6 h-6"></i>
        </button>

        <!-- 左侧导航栏 -->
        <nav class="sidebar fixed left-0 top-0 h-full w-64 z-40 transform transition-transform duration-300 lg:translate-x-0" 
             :class="{ '-translate-x-full': !sidebarOpen }">
            <div class="p-6 border-b border-white/20">
                <div class="flex items-center">
                    <div class="bg-white/20 p-2 rounded-lg mr-3">
                        <i data-lucide="shield-check" class="h-6 w-6 text-white"></i>
                    </div>
                    <div>
                        <h2 class="text-xl font-bold text-white">管理面板</h2>
                        <p class="text-sm text-white/70">ChanjetOAuth</p>
                    </div>
                </div>
            </div>
            
            <div class="p-4">
                <nav class="space-y-2">
                    <a href="/page/index.html" class="nav-link flex items-center px-4 py-3 text-white/80 hover:text-white hover:bg-white/20 rounded-lg transition-all">
                        <i data-lucide="home" class="w-5 h-5 mr-3"></i>
                        首页
                    </a>
                    <a href="/oauth/page" class="nav-link flex items-center px-4 py-3 text-white/80 hover:text-white hover:bg-white/20 rounded-lg transition-all">
                        <i data-lucide="key" class="w-5 h-5 mr-3"></i>
                        OAuth授权
                    </a>
                    <a href="/page/dashboard.html" class="nav-link flex items-center px-4 py-3 text-white/80 hover:text-white hover:bg-white/20 rounded-lg transition-all">
                        <i data-lucide="activity" class="w-5 h-5 mr-3"></i>
                        Token管理
                    </a>
                    <a href="/page/data-sync.html" class="nav-link flex items-center px-4 py-3 text-white bg-white/20 rounded-lg">
                        <i data-lucide="refresh-cw" class="w-5 h-5 mr-3"></i>
                        数据同步
                    </a>
                    <a href="#" @click.prevent="showFeatureComingSoon('系统监控')" class="nav-link flex items-center px-4 py-3 text-white/80 hover:text-white hover:bg-white/20 rounded-lg transition-all">
                        <i data-lucide="monitor" class="w-5 h-5 mr-3"></i>
                        系统监控
                    </a>
                    <a href="#" @click.prevent="showFeatureComingSoon('系统设置')" class="nav-link flex items-center px-4 py-3 text-white/80 hover:text-white hover:bg-white/20 rounded-lg transition-all">
                        <i data-lucide="settings" class="w-5 h-5 mr-3"></i>
                        系统设置
                    </a>
                </nav>
            </div>
        </nav>

        <!-- 主内容区域 -->
        <div class="lg:ml-64 min-h-screen">
            <div class="p-6">
                <!-- 页面标题 -->
                <div class="mb-8">
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">数据同步管理</h1>
                    <p class="text-gray-600">管理和监控数据同步任务的执行状态</p>
                </div>

                <!-- 控制面板 -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                    <!-- 同步控制 -->
                    <div class="lg:col-span-2 bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                        <div class="flex items-center justify-between mb-6">
                            <div class="flex items-center">
                                <div class="bg-blue-100 p-3 rounded-lg mr-4">
                                    <i data-lucide="settings" class="h-6 w-6 text-blue-600"></i>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900">同步控制</h3>
                                    <p class="text-sm text-gray-500">启动、停止和配置数据同步任务</p>
                                </div>
                            </div>
                            <div v-if="loading" class="flex items-center text-blue-600">
                                <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-2"></div>
                                <span class="text-sm">处理中...</span>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-2 lg:grid-cols-4 gap-4">
                            <button 
                                @click="startSync" 
                                :disabled="loading"
                                class="flex items-center justify-center px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                            >
                                <i data-lucide="play" class="w-4 h-4 mr-2"></i>
                                启动同步
                            </button>
                            <button 
                                @click="stopSync" 
                                :disabled="loading"
                                class="flex items-center justify-center px-4 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                            >
                                <i data-lucide="square" class="w-4 h-4 mr-2"></i>
                                停止同步
                            </button>
                            <button 
                                @click="refreshStatus" 
                                :disabled="loading"
                                class="flex items-center justify-center px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                            >
                                <i data-lucide="refresh-cw" class="w-4 h-4 mr-2"></i>
                                刷新状态
                            </button>
                            <button 
                                @click="refreshConfig" 
                                :disabled="loading"
                                class="flex items-center justify-center px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                            >
                                <i data-lucide="rotate-cw" class="w-4 h-4 mr-2"></i>
                                刷新配置
                            </button>
                        </div>
                    </div>

                    <!-- 快速状态 -->
                    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                        <div class="flex items-center mb-4">
                            <div class="bg-green-100 p-3 rounded-lg mr-4">
                                <i data-lucide="activity" class="h-6 w-6 text-green-600"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">快速状态</h3>
                                <p class="text-sm text-gray-500">系统运行概况</p>
                            </div>
                        </div>
                        
                        <div class="space-y-3">
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">总账户数</span>
                                <span class="font-semibold text-gray-900">{{ statusData.totalAccounts || 0 }}</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">运行任务</span>
                                <span class="font-semibold text-green-600">{{ statusData.runningTasks || 0 }}</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">系统状态</span>
                                <div class="flex items-center">
                                    <div :class="systemStatusClass" class="w-2 h-2 rounded-full mr-2"></div>
                                    <span class="text-sm font-medium">{{ systemStatusText }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 详细状态和API端点 -->
                <div class="grid grid-cols-1 xl:grid-cols-2 gap-6 mb-8">
                    <!-- 账户状态 -->
                    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                        <div class="flex items-center mb-6">
                            <div class="bg-purple-100 p-3 rounded-lg mr-4">
                                <i data-lucide="users" class="h-6 w-6 text-purple-600"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">账户状态</h3>
                                <p class="text-sm text-gray-500">各账户同步状态详情</p>
                            </div>
                        </div>
                        
                        <div class="space-y-4" v-if="accountStatus.length > 0">
                            <div 
                                v-for="account in accountStatus" 
                                :key="account.name"
                                class="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
                            >
                                <div class="flex items-center">
                                    <div :class="account.statusClass" class="w-3 h-3 rounded-full mr-3"></div>
                                    <div>
                                        <p class="font-medium text-gray-900">{{ account.name }}</p>
                                        <p class="text-sm text-gray-500">索引: {{ account.index }} | 用户: {{ account.username }}</p>
                                    </div>
                                </div>
                                <span :class="account.badgeClass" class="px-2 py-1 rounded-full text-xs font-medium">
                                    {{ account.status }}
                                </span>
                            </div>
                        </div>
                        <div v-else class="text-center py-8 text-gray-500">
                            <i data-lucide="inbox" class="h-12 w-12 mx-auto mb-3 opacity-50"></i>
                            <p>暂无账户信息</p>
                        </div>
                    </div>

                    <!-- API端点配置 -->
                    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                        <div class="flex items-center mb-6">
                            <div class="bg-orange-100 p-3 rounded-lg mr-4">
                                <i data-lucide="globe" class="h-6 w-6 text-orange-600"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">API端点</h3>
                                <p class="text-sm text-gray-500">配置的同步接口列表</p>
                            </div>
                        </div>
                        
                        <div class="space-y-3" v-if="endpoints.length > 0">
                            <div 
                                v-for="endpoint in endpoints" 
                                :key="endpoint.Name"
                                class="flex items-center justify-between p-3 border border-gray-200 rounded-lg"
                            >
                                <div class="flex-1">
                                    <p class="font-medium text-gray-900 mb-1">{{ endpoint.Name }}</p>
                                    <p class="text-xs text-gray-500">{{ endpoint.Method }} {{ endpoint.Path }}</p>
                                </div>
                                <span :class="endpoint.Enabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'" 
                                      class="px-2 py-1 rounded-full text-xs font-medium">
                                    {{ endpoint.Enabled ? '启用' : '禁用' }}
                                </span>
                            </div>
                        </div>
                        <div v-else class="text-center py-8 text-gray-500">
                            <i data-lucide="link" class="h-12 w-12 mx-auto mb-3 opacity-50"></i>
                            <p>暂无端点配置</p>
                        </div>
                    </div>
                </div>

                <!-- 实时日志 -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center justify-between mb-6">
                        <div class="flex items-center">
                            <div class="bg-gray-100 p-3 rounded-lg mr-4">
                                <i data-lucide="scroll-text" class="h-6 w-6 text-gray-600"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">实时日志</h3>
                                <p class="text-sm text-gray-500">系统运行日志和操作记录</p>
                            </div>
                        </div>
                        <button 
                            @click="clearLogs" 
                            class="flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
                        >
                            <i data-lucide="trash-2" class="w-4 h-4 mr-1"></i>
                            清空日志
                        </button>
                    </div>
                    
                    <div class="log-container bg-gray-900 rounded-lg p-4 h-96 overflow-y-auto font-mono text-sm">
                        <div 
                            v-for="(log, index) in logs" 
                            :key="index"
                            :class="log.type === 'error' ? 'text-red-400' : log.type === 'warning' ? 'text-yellow-400' : log.type === 'success' ? 'text-green-400' : 'text-gray-300'"
                            class="mb-1 leading-relaxed"
                        >
                            <span class="text-gray-500">[{{ log.time }}]</span> {{ log.message }}
                        </div>
                        <div v-if="logs.length === 0" class="text-gray-500 text-center py-8">
                            <i data-lucide="file-text" class="h-8 w-8 mx-auto mb-2 opacity-50"></i>
                            <p>暂无日志记录</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 通知提示 -->
        <div 
            v-if="notification.show"
            class="fixed top-6 right-6 max-w-sm bg-white border border-gray-200 rounded-xl shadow-lg p-4 z-50 transition-all duration-300"
            :class="notification.type === 'success' ? 'border-green-200' : notification.type === 'error' ? 'border-red-200' : 'border-blue-200'"
        >
            <div class="flex items-start">
                <div class="mr-3 mt-0.5" :class="notification.type === 'success' ? 'text-green-500' : notification.type === 'error' ? 'text-red-500' : 'text-blue-500'">
                    <i :data-lucide="notification.icon" class="h-5 w-5"></i>
                </div>
                <div class="flex-1">
                    <p class="font-medium text-gray-900">{{ notification.title }}</p>
                    <p class="text-sm text-gray-600 mt-1">{{ notification.message }}</p>
                </div>
                <button @click="hideNotification" class="text-gray-400 hover:text-gray-600 ml-2">
                    <i data-lucide="x" class="h-4 w-4"></i>
                </button>
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref, reactive, onMounted, onUnmounted } = Vue;

        createApp({
            setup() {
                // 响应式数据
                const sidebarOpen = ref(false);
                const loading = ref(false);
                const statusData = reactive({
                    totalAccounts: 0,
                    runningTasks: 0
                });
                const accountStatus = ref([]);
                const endpoints = ref([]);
                const logs = ref([]);
                const systemStatusClass = ref('bg-yellow-400');
                const systemStatusText = ref('检查中');
                
                const notification = reactive({
                    show: false,
                    type: 'info',
                    title: '',
                    message: '',
                    icon: 'info'
                });

                let statusUpdateInterval;

                // 方法
                const toggleSidebar = () => {
                    sidebarOpen.value = !sidebarOpen.value;
                };

                const showFeatureComingSoon = (featureName) => {
                    showNotification('info', '功能即将推出', `${featureName}功能正在开发中，敬请期待！`);
                };

                const showNotification = (type, title, message) => {
                    notification.show = true;
                    notification.type = type;
                    notification.title = title;
                    notification.message = message;
                    notification.icon = type === 'success' ? 'check-circle' : type === 'error' ? 'alert-circle' : 'info';
                    
                    setTimeout(() => {
                        hideNotification();
                    }, 5000);
                };

                const hideNotification = () => {
                    notification.show = false;
                };

                const addLog = (message, type = 'info') => {
                    const timestamp = new Date().toLocaleTimeString();
                    logs.value.push({
                        time: timestamp,
                        message: message,
                        type: type
                    });
                    
                    // 限制日志条数
                    if (logs.value.length > 100) {
                        logs.value.shift();
                    }
                    
                    // 滚动到底部
                    setTimeout(() => {
                        const container = document.querySelector('.log-container');
                        if (container) {
                            container.scrollTop = container.scrollHeight;
                        }
                    }, 100);
                };

                const clearLogs = () => {
                    logs.value = [];
                    addLog('日志已清空', 'info');
                };

                // API调用方法
                const startSync = async () => {
                    loading.value = true;
                    addLog('正在启动数据同步任务...', 'info');
                    
                    try {
                        const response = await fetch('/api/datasync/start', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' }
                        });
                        
                        const result = await response.json();
                        
                        if (result.success) {
                            addLog('数据同步任务启动成功', 'success');
                            showNotification('success', '启动成功', '数据同步任务已启动');
                            setTimeout(refreshStatus, 2000);
                        } else {
                            addLog(`启动失败: ${result.message}`, 'error');
                            showNotification('error', '启动失败', result.message);
                        }
                    } catch (error) {
                        addLog(`启动失败: ${error.message}`, 'error');
                        showNotification('error', '启动失败', '网络错误或服务不可用');
                    } finally {
                        loading.value = false;
                    }
                };

                const stopSync = async () => {
                    loading.value = true;
                    addLog('正在停止数据同步任务...', 'info');
                    
                    try {
                        const response = await fetch('/api/datasync/stop', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' }
                        });
                        
                        const result = await response.json();
                        
                        if (result.success) {
                            addLog('数据同步任务已停止', 'success');
                            showNotification('success', '停止成功', '数据同步任务已停止');
                            setTimeout(refreshStatus, 2000);
                        } else {
                            addLog(`停止失败: ${result.message}`, 'error');
                            showNotification('error', '停止失败', result.message);
                        }
                    } catch (error) {
                        addLog(`停止失败: ${error.message}`, 'error');
                        showNotification('error', '停止失败', '网络错误或服务不可用');
                    } finally {
                        loading.value = false;
                    }
                };

                const refreshStatus = async () => {
                    try {
                        const response = await fetch('/api/datasync/status');
                        const result = await response.json();
                        
                        if (result.success) {
                            updateStatusDisplay(result.data);
                            addLog('状态刷新成功', 'success');
                        } else {
                            addLog(`获取状态失败: ${result.message}`, 'error');
                            systemStatusClass.value = 'bg-red-500';
                            systemStatusText.value = '异常';
                        }
                    } catch (error) {
                        addLog(`获取状态失败: ${error.message}`, 'error');
                        systemStatusClass.value = 'bg-red-500';
                        systemStatusText.value = '离线';
                    }
                };

                const refreshConfig = async () => {
                    loading.value = true;
                    addLog('正在刷新配置...', 'info');
                    
                    try {
                        const response = await fetch('/api/datasync/refresh-config', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' }
                        });
                        
                        const result = await response.json();
                        
                        if (result.success) {
                            addLog('配置刷新成功', 'success');
                            showNotification('success', '刷新成功', '配置已刷新');
                            loadEndpoints();
                        } else {
                            addLog(`配置刷新失败: ${result.message}`, 'error');
                            showNotification('error', '刷新失败', result.message);
                        }
                    } catch (error) {
                        addLog(`配置刷新失败: ${error.message}`, 'error');
                        showNotification('error', '刷新失败', '网络错误或服务不可用');
                    } finally {
                        loading.value = false;
                    }
                };

                const loadEndpoints = async () => {
                    try {
                        const response = await fetch('/api/datasync/endpoints');
                        const result = await response.json();
                        
                        if (result.success) {
                            endpoints.value = result.data;
                        } else {
                            addLog(`获取端点配置失败: ${result.message}`, 'error');
                        }
                    } catch (error) {
                        addLog(`获取端点配置失败: ${error.message}`, 'error');
                    }
                };

                const updateStatusDisplay = (data) => {
                    statusData.totalAccounts = data['总账户数'] || 0;
                    statusData.runningTasks = data['运行中任务数'] || 0;
                    
                    // 更新系统状态
                    if (statusData.runningTasks > 0) {
                        systemStatusClass.value = 'bg-green-500';
                        systemStatusText.value = '运行中';
                    } else if (statusData.totalAccounts > 0) {
                        systemStatusClass.value = 'bg-yellow-500';
                        systemStatusText.value = '就绪';
                    } else {
                        systemStatusClass.value = 'bg-gray-500';
                        systemStatusText.value = '待配置';
                    }
                    
                    // 更新账户状态
                    const accounts = data['账户状态'] || {};
                    accountStatus.value = Object.keys(accounts).map(companyName => {
                        const account = accounts[companyName];
                        const isRunning = account['运行状态'] === '运行中';
                        
                        return {
                            name: companyName,
                            index: account['账户索引'] || '-',
                            username: account['用户名'] || '-',
                            status: account['运行状态'] || '未知',
                            statusClass: isRunning ? 'bg-green-500' : 'bg-gray-400',
                            badgeClass: isRunning ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'
                        };
                    });
                };

                // 生命周期
                onMounted(() => {
                    // 初始化Lucide图标
                    lucide.createIcons();
                    
                    // 初始化日志
                    addLog('数据同步管理页面已加载', 'info');
                    
                    // 初始加载数据
                    refreshStatus();
                    loadEndpoints();
                    
                    // 设置定时刷新状态
                    statusUpdateInterval = setInterval(refreshStatus, 30000);
                    
                    addLog('系统启动完成，开始监控数据同步状态', 'info');
                });

                onUnmounted(() => {
                    if (statusUpdateInterval) {
                        clearInterval(statusUpdateInterval);
                    }
                });

                return {
                    sidebarOpen,
                    loading,
                    statusData,
                    accountStatus,
                    endpoints,
                    logs,
                    systemStatusClass,
                    systemStatusText,
                    notification,
                    toggleSidebar,
                    showFeatureComingSoon,
                    hideNotification,
                    startSync,
                    stopSync,
                    refreshStatus,
                    refreshConfig,
                    clearLogs
                };
            }
        }).mount('#app');
    </script>
</body>
</html> 