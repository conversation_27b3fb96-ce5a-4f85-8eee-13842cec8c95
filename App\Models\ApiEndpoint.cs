using System;

namespace ChanjetOAuthTest.App.Models
{
    /// <summary>
    /// API接口端点配置类
    /// </summary>
    public class ApiEndpoint
    {
        /// <summary>
        /// 接口名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 接口路径
        /// </summary>
        public string Path { get; set; }

        /// <summary>
        /// 请求类型（默认为POST）
        /// </summary>
        public string Method { get; set; } = "POST";

        /// <summary>
        /// 请求体模板
        /// </summary>
        public string RequestBodyTemplate { get; set; } = "{}";

        /// <summary>
        /// 接口描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool Enabled { get; set; } = true;
    }
} 