import { ref } from 'vue'
import type { Post } from '@/types'
import { apiMethods } from '@/api'

export function useApi() {
  const loading = ref(false)
  const error = ref<string>('')
  
  // 通用请求方法
  const request = async <T>(apiCall: () => Promise<any>): Promise<T | null> => {
    loading.value = true
    error.value = ''
    
    try {
      const response = await apiCall()
      return response.data
    } catch (err) {
      error.value = err instanceof Error ? err.message : '请求失败'
      return null
    } finally {
      loading.value = false
    }
  }
  
  return {
    loading,
    error,
    request
  }
}

// 文章相关composable
export function usePosts() {
  const posts = ref<Post[]>([])
  const currentPost = ref<Post | null>(null)
  const { loading, error, request } = useApi()
  
  const fetchPosts = async () => {
    const data = await request<Post[]>(() => apiMethods.getPosts())
    if (data) {
      posts.value = data
    }
  }
  
  const fetchPost = async (id: number) => {
    const data = await request<Post>(() => apiMethods.getPost(id))
    if (data) {
      currentPost.value = data
    }
  }
  
  const createPost = async (postData: Omit<Post, 'id'>) => {
    const data = await request<Post>(() => apiMethods.createPost(postData))
    if (data) {
      posts.value.push(data)
    }
    return data
  }
  
  const updatePost = async (id: number, postData: Partial<Post>) => {
    const data = await request<Post>(() => apiMethods.updatePost(id, postData))
    if (data) {
      const index = posts.value.findIndex(p => p.id === id)
      if (index !== -1) {
        posts.value[index] = data
      }
    }
    return data
  }
  
  const deletePost = async (id: number) => {
    const success = await request(() => apiMethods.deletePost(id))
    if (success) {
      posts.value = posts.value.filter(p => p.id !== id)
    }
    return success
  }
  
  return {
    posts,
    currentPost,
    loading,
    error,
    fetchPosts,
    fetchPost,
    createPost,
    updatePost,
    deletePost
  }
} 
