using ChanjetOAuthTest.App.Models;
using System;
using System.Threading.Tasks;

namespace ChanjetOAuthTest.App.Interfaces
{
    /// <summary>
    /// Token管理器接口，负责OAuth令牌的获取、刷新、验证和监控
    /// </summary>
    public interface ITokenManager
    {
        /// <summary>
        /// 通过授权码获取访问令牌
        /// </summary>
        /// <param name="authCode">OAuth授权码</param>
        /// <param name="userId">用户ID，可选参数</param>
        /// <returns>返回包含访问令牌信息的TokenInfo对象</returns>
        Task<TokenInfo> GetTokenByAuthCodeAsync(string authCode, string userId = null);

        /// <summary>
        /// 获取有效的访问令牌，如果令牌已过期则自动刷新
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>返回有效的TokenInfo对象</returns>
        Task<TokenInfo> GetValidTokenAsync(string userId);

        /// <summary>
        /// 获取用户的令牌信息
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>返回TokenInfo对象，可能包含已过期的令牌</returns>
        Task<TokenInfo> GetTokenInfoAsync(string userId);

        /// <summary>
        /// 刷新用户的访问令牌
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>返回刷新后的TokenInfo对象</returns>
        Task<TokenInfo> RefreshTokenAsync(string userId);

        /// <summary>
        /// 检查用户的令牌是否有效
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>如果令牌有效返回true，否则返回false</returns>
        Task<bool> IsTokenValidAsync(string userId);

        /// <summary>
        /// 启动令牌监控服务，定期检查令牌状态
        /// </summary>
        void StartTokenMonitoring();

        /// <summary>
        /// 停止令牌监控服务
        /// </summary>
        void StopTokenMonitoring();

        /// <summary>
        /// 令牌过期事件，当检测到令牌过期时触发
        /// </summary>
        event EventHandler<TokenExpiredEventArgs> TokenExpired;

        /// <summary>
        /// 令牌刷新事件，当令牌成功刷新时触发
        /// </summary>
        event EventHandler<TokenRefreshedEventArgs> TokenRefreshed;
    }

    /// <summary>
    /// 令牌过期事件参数
    /// </summary>
    public class TokenExpiredEventArgs : EventArgs
    {
        /// <summary>
        /// 令牌过期的用户ID
        /// </summary>
        public string UserId { get; set; }

        /// <summary>
        /// 已过期的令牌信息
        /// </summary>
        public TokenInfo ExpiredToken { get; set; }
    }

    /// <summary>
    /// 令牌刷新事件参数
    /// </summary>
    public class TokenRefreshedEventArgs : EventArgs
    {
        /// <summary>
        /// 令牌刷新的用户ID
        /// </summary>
        public string UserId { get; set; }

        /// <summary>
        /// 旧的令牌信息
        /// </summary>
        public TokenInfo OldToken { get; set; }

        /// <summary>
        /// 新的令牌信息
        /// </summary>
        public TokenInfo NewToken { get; set; }
    }
} 