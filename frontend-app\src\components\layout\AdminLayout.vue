<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { getNavigationItemByPath } from '@/config/navigation'
import { useSystemStatus } from '@/composables/useSystemStatus'

import BaseLayout from './BaseLayout.vue'
import AppSidebar from './AppSidebar.vue'
import AppHeader from './AppHeader.vue'

const route = useRoute()

// 系统状态管理
const {
  systemStatus,
  isLoading: isStatusLoading,
  lastUpdated,
  refreshSystemStatus,
  getStatusColor
} = useSystemStatus()

// 当前页面信息
const currentPageInfo = computed(() => {
  const currentItem = getNavigationItemByPath(route.path)
  return currentItem ? { name: currentItem.title, href: currentItem.href } : { name: '管理面板', href: '/' }
})

// 面包屑导航数组
const breadcrumbItems = computed(() => {
  const items = []
  
  if (route.path !== '/') {
    items.push({ name: '管理面板', href: '/' })
  }
  
  const currentItem = getNavigationItemByPath(route.path)
  if (currentItem && route.path !== '/') {
    items.push({ name: currentItem.title, href: route.path })
  }
  
  return items
})

// 通知数量
const notificationCount = computed(() => 3)

// 系统状态项列表
const statusItems = computed(() => [
  { key: 'oauth', ...systemStatus.value.oauth },
  { key: 'token', ...systemStatus.value.token },
  { key: 'sync', ...systemStatus.value.sync }
])

// 格式化最后更新时间
const formattedLastUpdated = computed(() => {
  if (!lastUpdated.value) return ''
  
  const now = new Date()
  const diff = now.getTime() - lastUpdated.value.getTime()
  const minutes = Math.floor(diff / 60000)
  
  if (minutes < 1) return '刚刚更新'
  if (minutes < 60) return `${minutes}分钟前更新`
  
  const hours = Math.floor(minutes / 60)
  if (hours < 24) return `${hours}小时前更新`
  
  return lastUpdated.value.toLocaleDateString()
})
</script>

<template>
  <BaseLayout
    main-class="container mx-auto px-6 py-8 max-w-7xl"
    header-class="sticky top-0 z-40 flex h-16 items-center justify-between bg-white/95 backdrop-blur-xl border-b border-slate-200/60 shadow-sm px-6"
  >
    <template #sidebar>
      <AppSidebar />
    </template>

    <template #header>
      <AppHeader
        :current-page-info="currentPageInfo"
        :breadcrumb-items="breadcrumbItems"
        :notification-count="notificationCount"
        :status-items="statusItems"
        :formatted-last-updated="formattedLastUpdated"
        :is-status-loading="isStatusLoading"
        :get-status-color="(status: string) => getStatusColor(status as 'healthy' | 'warning' | 'error' | 'unknown')"
        @refresh-status="refreshSystemStatus"
      />
    </template>

    <slot />
  </BaseLayout>
</template> 
