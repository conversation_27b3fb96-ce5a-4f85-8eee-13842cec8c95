import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Dashboard',
    component: () => import('@/views/Dashboard.vue'),
    meta: { title: '管理面板' }
  },
  {
    path: '/oauth',
    name: 'OAuth',
    component: () => import('@/views/OAuth.vue'),
    meta: { title: 'OAuth授权' }
  },
  {
    path: '/tokens',
    name: 'TokenManagement',
    component: () => import('@/views/TokenManagement.vue'),
    meta: { title: 'Token管理' }
  },
  {
    path: '/data-sync',
    name: 'DataSync',
    component: () => import('@/views/DataSync.vue'),
    meta: { title: '数据同步' }
  },
  {
    path: '/monitoring',
    name: 'Monitoring',
    component: () => import('@/views/Monitoring.vue'),
    meta: { title: '系统监控' }
  },
  {
    path: '/settings',
    name: 'Settings',
    component: () => import('@/views/Settings.vue'),
    meta: { title: '系统设置' }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, _from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - ChanjetOAuth 管理系统`
  }
  next()
})

export default router 
