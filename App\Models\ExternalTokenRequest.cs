using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace ChanjetOAuthTest.App.Models
{
    /// <summary>
    /// 外部Token请求模型
    /// </summary>
    public class ExternalTokenRequest
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Required(ErrorMessage = "用户ID不能为空")]
        [JsonPropertyName("user_id")]
        public string UserId { get; set; }

        /// <summary>
        /// 客户端ID
        /// </summary>
        [Required(ErrorMessage = "客户端ID不能为空")]
        [JsonPropertyName("client_id")]
        public string ClientId { get; set; }

        /// <summary>
        /// 客户端密钥
        /// </summary>
        [Required(ErrorMessage = "客户端密钥不能为空")]
        [JsonPropertyName("client_secret")]
        public string ClientSecret { get; set; }

        /// <summary>
        /// 请求的作用域
        /// </summary>
        [JsonPropertyName("scope")]
        public string Scope { get; set; }

        /// <summary>
        /// 额外的请求参数
        /// </summary>
        [JsonPropertyName("additional_params")]
        public string AdditionalParams { get; set; }
    }

    /// <summary>
    /// 外部Token刷新请求模型
    /// </summary>
    public class ExternalTokenRefreshRequest
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Required(ErrorMessage = "用户ID不能为空")]
        [JsonPropertyName("user_id")]
        public string UserId { get; set; }

        /// <summary>
        /// 客户端ID
        /// </summary>
        [Required(ErrorMessage = "客户端ID不能为空")]
        [JsonPropertyName("client_id")]
        public string ClientId { get; set; }

        /// <summary>
        /// 客户端密钥
        /// </summary>
        [Required(ErrorMessage = "客户端密钥不能为空")]
        [JsonPropertyName("client_secret")]
        public string ClientSecret { get; set; }

        /// <summary>
        /// 刷新Token（可选，如果不提供则使用存储的RefreshToken）
        /// </summary>
        [JsonPropertyName("refresh_token")]
        public string RefreshToken { get; set; }
    }

    /// <summary>
    /// 外部Token验证请求模型
    /// </summary>
    public class ExternalTokenValidateRequest
    {
        /// <summary>
        /// 访问Token
        /// </summary>
        [Required(ErrorMessage = "访问Token不能为空")]
        [JsonPropertyName("access_token")]
        public string AccessToken { get; set; }

        /// <summary>
        /// 客户端ID
        /// </summary>
        [Required(ErrorMessage = "客户端ID不能为空")]
        [JsonPropertyName("client_id")]
        public string ClientId { get; set; }
    }
} 