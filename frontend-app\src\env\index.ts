// 环境配置管理
export interface AppConfig {
  apiBaseUrl: string
  appName: string
  version: string
  environment: 'development' | 'staging' | 'production'
  enableMock: boolean
  logLevel: 'debug' | 'info' | 'warn' | 'error'
}

// 默认配置
const defaultConfig: AppConfig = {
  apiBaseUrl: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080/api',
  appName: import.meta.env.VITE_APP_NAME || 'Frontend App',
  version: import.meta.env.VITE_APP_VERSION || '1.0.0',
  environment: (import.meta.env.VITE_NODE_ENV as AppConfig['environment']) || 'development',
  enableMock: import.meta.env.VITE_ENABLE_MOCK === 'true',
  logLevel: (import.meta.env.VITE_LOG_LEVEL as AppConfig['logLevel']) || 'info'
}

// 导出配置
export const appConfig: AppConfig = {
  ...defaultConfig,
  // 可以在这里添加环境特定的配置覆盖
}

// 便捷方法
export const isDevelopment = () => appConfig.environment === 'development'
export const isProduction = () => appConfig.environment === 'production'
export const isStaging = () => appConfig.environment === 'staging' 
