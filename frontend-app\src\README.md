# 项目目录结构说明

## 目录结构概览

```
src/
├── api/                    # API 接口层
│   └── index.ts           # API 客户端配置和接口定义
├── assets/                # 静态资源
│   ├── images/            # 图片资源
│   ├── styles/            # 样式文件
│   │   └── global.css     # 全局样式
│   └── fonts/             # 字体文件
├── components/            # 组件
│   ├── ui/                # UI 基础组件(reka-ui等)
│   ├── common/            # 通用业务组件
│   ├── layout/            # 布局组件
│   └── business/          # 业务特定组件
├── composables/           # Vue3 组合式 API
│   ├── useApi.ts          # API 相关组合函数
│   ├── useSearch.ts       # 搜索功能组合函数
│   └── useSystemStatus.ts # 系统状态组合函数
├── config/                # 配置文件
│   └── navigation.ts      # 导航配置
├── constants/             # 常量定义
│   └── index.ts          # 应用常量
├── env/                   # 环境配置
│   └── index.ts          # 环境变量管理
├── hooks/                 # 自定义 hooks
│   └── index.ts          # 通用 hooks 集合
├── middleware/            # 中间件
│   └── index.ts          # 路由中间件
├── mock/                  # 模拟数据
│   └── index.ts          # 模拟 API 数据
├── router/                # 路由配置
│   └── index.ts          # Vue Router 配置
├── services/              # 业务服务层
│   └── index.ts          # 业务逻辑服务
├── stores/                # 状态管理 (Pinia)
│   ├── index.ts          # Store 配置
│   └── counter.ts        # 示例 Store
├── types/                 # TypeScript 类型定义
│   ├── index.ts          # 应用类型定义
│   └── env.d.ts          # 环境类型定义
├── utils/                 # 工具函数
│   └── index.ts          # 通用工具函数
├── views/                 # 页面组件
│   ├── Dashboard.vue      # 仪表板页面
│   ├── TokenManagement.vue # Token 管理页面
│   ├── Settings.vue       # 设置页面
│   ├── Monitoring.vue     # 监控页面
│   ├── DataSync.vue       # 数据同步页面
│   └── OAuth.vue         # OAuth 页面
├── App.vue               # 根组件
├── main.ts               # 应用入口
└── vite-env.d.ts         # Vite 环境类型
```

## 目录职责说明

### 🔌 api/
- 负责所有与后端 API 的交互
- 配置 axios 实例
- 定义 API 接口方法
- 处理请求/响应拦截

### 🎨 assets/
- **images/**: 存放图片、图标等静态资源
- **styles/**: 存放全局样式、主题等
- **fonts/**: 存放字体文件

### 🧩 components/
- **ui/**: 基础 UI 组件，如按钮、输入框等
- **common/**: 跨页面复用的通用组件
- **layout/**: 页面布局组件，如头部、侧边栏等
- **business/**: 特定业务逻辑组件

### 🔄 composables/
- Vue3 组合式 API 函数
- 封装可复用的响应式逻辑
- 遵循 `use` 命名约定

### ⚙️ config/
- 应用配置文件
- 导航配置、主题配置等

### 📋 constants/
- 应用常量定义
- 避免魔法数字和字符串

### 🌍 env/
- 环境变量管理
- 不同环境的配置

### 🎣 hooks/
- 自定义 React-style hooks
- 封装复杂的状态逻辑

### 🚦 middleware/
- 路由中间件
- 权限控制、页面标题设置等

### 🎭 mock/
- 模拟数据和 API
- 开发阶段使用

### 🗂️ services/
- 业务逻辑服务层
- 处理复杂的业务逻辑
- 与 API 层协作

### 🗃️ stores/
- Pinia 状态管理
- 全局状态定义

### 📝 types/
- TypeScript 类型定义
- 接口和类型声明

### 🛠️ utils/
- 工具函数
- 通用帮助方法

### 📄 views/
- 页面级组件
- 路由对应的页面

## 最佳实践

1. **文件命名**：使用 PascalCase 命名组件文件，camelCase 命名工具文件
2. **导入顺序**：第三方库 → 内部模块 → 相对路径
3. **类型安全**：充分利用 TypeScript 类型检查
4. **组件粒度**：保持组件单一职责，合理拆分
5. **状态管理**：合理使用 Pinia 管理全局状态，避免过度集中

## 架构优势

- 🔍 **清晰的职责分离**：每个目录都有明确的职责
- 🔄 **高复用性**：组件和逻辑可以轻松复用
- 🧪 **易于测试**：模块化结构便于单元测试
- 📈 **可扩展性**：新功能可以按照既定结构添加
- 🤝 **团队协作**：标准化的结构便于团队协作 