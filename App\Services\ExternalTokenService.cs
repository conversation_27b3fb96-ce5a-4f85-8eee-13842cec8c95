using ChanjetOAuthTest.App.Models;
using ChanjetOAuthTest.App.Interfaces;
using ChanjetOAuthTest.App.Configuration;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ChanjetOAuthTest.App.Services
{
    /// <summary>
    /// 外部Token服务实现类
    /// </summary>
    public class ExternalTokenService : IExternalTokenService
    {
        private readonly ITokenManager _tokenManager;
        private readonly ITokenStorage _tokenStorage;
        private readonly ChanjetConfig _config;
        private readonly ExternalTokenConfig _externalTokenConfig;
        private readonly ILogger<ExternalTokenService> _logger;
        
        // 简单的客户端验证存储（从配置加载）
        private readonly Dictionary<string, string> _clientCredentials;
        
        // Token使用记录存储（实际项目中应该使用数据库）
        private readonly List<TokenUsageRecord> _usageRecords;

        public ExternalTokenService(
            ITokenManager tokenManager,
            ITokenStorage tokenStorage,
            IOptions<ChanjetConfig> config,
            IOptions<ExternalTokenConfig> externalTokenConfig,
            ILogger<ExternalTokenService> logger)
        {
            _tokenManager = tokenManager ?? throw new ArgumentNullException(nameof(tokenManager));
            _tokenStorage = tokenStorage ?? throw new ArgumentNullException(nameof(tokenStorage));
            _config = config?.Value ?? throw new ArgumentNullException(nameof(config));
            _externalTokenConfig = externalTokenConfig?.Value ?? throw new ArgumentNullException(nameof(externalTokenConfig));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            // 从配置初始化客户端凭据
            _clientCredentials = new Dictionary<string, string>();
            if (_externalTokenConfig.ClientCredentials != null)
            {
                foreach (var credential in _externalTokenConfig.ClientCredentials)
                {
                    if (!string.IsNullOrEmpty(credential.ClientId) && !string.IsNullOrEmpty(credential.ClientSecret))
                    {
                        _clientCredentials[credential.ClientId] = credential.ClientSecret;
                    }
                }
            }

            _usageRecords = new List<TokenUsageRecord>();
            
            _logger.LogInformation("ExternalTokenService initialized with {Count} client credentials", _clientCredentials.Count);
        }

        /// <summary>
        /// 获取Token（外部接口）
        /// </summary>
        public async Task<ExternalTokenAcquireResponse> AcquireTokenAsync(ExternalTokenRequest request, ClientInfo clientInfo)
        {
            var traceId = clientInfo?.RequestId ?? Guid.NewGuid().ToString();
            var startTime = DateTime.UtcNow;

            try
            {
                _logger.LogInformation("开始处理外部Token获取请求, UserId: {UserId}, ClientId: {ClientId}, TraceId: {TraceId}", 
                    request.UserId, request.ClientId, traceId);

                // 验证客户端凭据
                if (!await ValidateClientCredentialsAsync(request.ClientId, request.ClientSecret))
                {
                    await RecordUsageAsync(request.UserId, request.ClientId, clientInfo, TokenRequestType.Acquire, false, "客户端凭据验证失败", startTime);
                    return new ExternalTokenAcquireResponse
                    {
                        Success = false,
                        ErrorCode = "INVALID_CLIENT",
                        ErrorMessage = "客户端凭据验证失败",
                        TraceId = traceId
                    };
                }

                // 获取有效Token
                var tokenInfo = await _tokenManager.GetValidTokenAsync(request.UserId);
                if (tokenInfo == null)
                {
                    await RecordUsageAsync(request.UserId, request.ClientId, clientInfo, TokenRequestType.Acquire, false, "用户Token不存在或已过期", startTime);
                    return new ExternalTokenAcquireResponse
                    {
                        Success = false,
                        ErrorCode = "TOKEN_NOT_FOUND",
                        ErrorMessage = "用户Token不存在或已过期，请重新授权",
                        TraceId = traceId
                    };
                }

                // 记录成功使用
                await RecordUsageAsync(request.UserId, request.ClientId, clientInfo, TokenRequestType.Acquire, true, null, startTime);

                _logger.LogInformation("外部Token获取成功, UserId: {UserId}, ClientId: {ClientId}, TraceId: {TraceId}", 
                    request.UserId, request.ClientId, traceId);

                return new ExternalTokenAcquireResponse
                {
                    Success = true,
                    Token = tokenInfo,
                    AppKey = _config.AppKey,
                    AppSecret = _config.AppSecret,
                    OpenToken = tokenInfo.AccessToken, // 使用AccessToken作为OpenToken
                    TraceId = traceId
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理外部Token获取请求时发生异常, UserId: {UserId}, ClientId: {ClientId}, TraceId: {TraceId}", 
                    request.UserId, request.ClientId, traceId);

                await RecordUsageAsync(request.UserId, request.ClientId, clientInfo, TokenRequestType.Acquire, false, ex.Message, startTime);

                return new ExternalTokenAcquireResponse
                {
                    Success = false,
                    ErrorCode = "INTERNAL_ERROR",
                    ErrorMessage = "服务内部错误",
                    TraceId = traceId
                };
            }
        }

        /// <summary>
        /// 刷新Token（外部接口）
        /// </summary>
        public async Task<ExternalTokenAcquireResponse> RefreshTokenAsync(ExternalTokenRefreshRequest request, ClientInfo clientInfo)
        {
            var traceId = clientInfo?.RequestId ?? Guid.NewGuid().ToString();
            var startTime = DateTime.UtcNow;

            try
            {
                _logger.LogInformation("开始处理外部Token刷新请求, UserId: {UserId}, ClientId: {ClientId}, TraceId: {TraceId}", 
                    request.UserId, request.ClientId, traceId);

                // 验证客户端凭据
                if (!await ValidateClientCredentialsAsync(request.ClientId, request.ClientSecret))
                {
                    await RecordUsageAsync(request.UserId, request.ClientId, clientInfo, TokenRequestType.Refresh, false, "客户端凭据验证失败", startTime);
                    return new ExternalTokenAcquireResponse
                    {
                        Success = false,
                        ErrorCode = "INVALID_CLIENT",
                        ErrorMessage = "客户端凭据验证失败",
                        TraceId = traceId
                    };
                }

                // 刷新Token
                var tokenInfo = await _tokenManager.RefreshTokenAsync(request.UserId);
                if (tokenInfo == null)
                {
                    await RecordUsageAsync(request.UserId, request.ClientId, clientInfo, TokenRequestType.Refresh, false, "Token刷新失败", startTime);
                    return new ExternalTokenAcquireResponse
                    {
                        Success = false,
                        ErrorCode = "REFRESH_FAILED",
                        ErrorMessage = "Token刷新失败，请重新授权",
                        TraceId = traceId
                    };
                }

                // 记录成功使用
                await RecordUsageAsync(request.UserId, request.ClientId, clientInfo, TokenRequestType.Refresh, true, null, startTime);

                _logger.LogInformation("外部Token刷新成功, UserId: {UserId}, ClientId: {ClientId}, TraceId: {TraceId}", 
                    request.UserId, request.ClientId, traceId);

                return new ExternalTokenAcquireResponse
                {
                    Success = true,
                    Token = tokenInfo,
                    AppKey = _config.AppKey,
                    AppSecret = _config.AppSecret,
                    OpenToken = tokenInfo.AccessToken,
                    TraceId = traceId
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理外部Token刷新请求时发生异常, UserId: {UserId}, ClientId: {ClientId}, TraceId: {TraceId}", 
                    request.UserId, request.ClientId, traceId);

                await RecordUsageAsync(request.UserId, request.ClientId, clientInfo, TokenRequestType.Refresh, false, ex.Message, startTime);

                return new ExternalTokenAcquireResponse
                {
                    Success = false,
                    ErrorCode = "INTERNAL_ERROR",
                    ErrorMessage = "服务内部错误",
                    TraceId = traceId
                };
            }
        }

        /// <summary>
        /// 验证Token（外部接口）
        /// </summary>
        public async Task<ExternalTokenValidateResponse> ValidateTokenAsync(ExternalTokenValidateRequest request, ClientInfo clientInfo)
        {
            var traceId = clientInfo?.RequestId ?? Guid.NewGuid().ToString();
            var startTime = DateTime.UtcNow;

            try
            {
                _logger.LogInformation("开始处理外部Token验证请求, ClientId: {ClientId}, TraceId: {TraceId}", 
                    request.ClientId, traceId);

                // 简单的Token验证逻辑（实际项目中需要更复杂的验证）
                var allTokens = await _tokenStorage.GetAllTokensAsync();
                var tokenInfo = allTokens.Values.FirstOrDefault(t => t.AccessToken == request.AccessToken);

                if (tokenInfo == null)
                {
                    await RecordUsageAsync("unknown", request.ClientId, clientInfo, TokenRequestType.Validate, false, "Token不存在", startTime);
                    return new ExternalTokenValidateResponse
                    {
                        Success = true,
                        IsValid = false,
                        ErrorMessage = "Token不存在",
                        TraceId = traceId
                    };
                }

                var isValid = !tokenInfo.IsAccessTokenExpired;
                await RecordUsageAsync(tokenInfo.UserId, request.ClientId, clientInfo, TokenRequestType.Validate, true, null, startTime);

                _logger.LogInformation("外部Token验证完成, IsValid: {IsValid}, UserId: {UserId}, ClientId: {ClientId}, TraceId: {TraceId}", 
                    isValid, tokenInfo.UserId, request.ClientId, traceId);

                return new ExternalTokenValidateResponse
                {
                    Success = true,
                    IsValid = isValid,
                    ExpiresAt = tokenInfo.ExpiresAt,
                    UserId = tokenInfo.UserId,
                    Scope = tokenInfo.Scope,
                    TraceId = traceId
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理外部Token验证请求时发生异常, ClientId: {ClientId}, TraceId: {TraceId}", 
                    request.ClientId, traceId);

                await RecordUsageAsync("unknown", request.ClientId, clientInfo, TokenRequestType.Validate, false, ex.Message, startTime);

                return new ExternalTokenValidateResponse
                {
                    Success = false,
                    ErrorCode = "INTERNAL_ERROR",
                    ErrorMessage = "服务内部错误",
                    TraceId = traceId
                };
            }
        }

        /// <summary>
        /// 撤销Token（外部接口）
        /// </summary>
        public async Task<ExternalTokenResponse> RevokeTokenAsync(string userId, string clientId, string clientSecret, ClientInfo clientInfo)
        {
            var traceId = clientInfo?.RequestId ?? Guid.NewGuid().ToString();
            var startTime = DateTime.UtcNow;

            try
            {
                _logger.LogInformation("开始处理外部Token撤销请求, UserId: {UserId}, ClientId: {ClientId}, TraceId: {TraceId}", 
                    userId, clientId, traceId);

                // 验证客户端凭据
                if (!await ValidateClientCredentialsAsync(clientId, clientSecret))
                {
                    await RecordUsageAsync(userId, clientId, clientInfo, TokenRequestType.Revoke, false, "客户端凭据验证失败", startTime);
                    return new ExternalTokenResponse
                    {
                        Success = false,
                        ErrorCode = "INVALID_CLIENT",
                        ErrorMessage = "客户端凭据验证失败",
                        TraceId = traceId
                    };
                }

                // 删除Token
                await _tokenStorage.RemoveTokenAsync(userId);
                await RecordUsageAsync(userId, clientId, clientInfo, TokenRequestType.Revoke, true, null, startTime);

                _logger.LogInformation("外部Token撤销成功, UserId: {UserId}, ClientId: {ClientId}, TraceId: {TraceId}", 
                    userId, clientId, traceId);

                return new ExternalTokenResponse
                {
                    Success = true,
                    TraceId = traceId
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理外部Token撤销请求时发生异常, UserId: {UserId}, ClientId: {ClientId}, TraceId: {TraceId}", 
                    userId, clientId, traceId);

                await RecordUsageAsync(userId, clientId, clientInfo, TokenRequestType.Revoke, false, ex.Message, startTime);

                return new ExternalTokenResponse
                {
                    Success = false,
                    ErrorCode = "INTERNAL_ERROR",
                    ErrorMessage = "服务内部错误",
                    TraceId = traceId
                };
            }
        }

        /// <summary>
        /// 验证客户端凭据
        /// </summary>
        public async Task<bool> ValidateClientCredentialsAsync(string clientId, string clientSecret)
        {
            await Task.CompletedTask; // 保持异步接口一致性

            if (string.IsNullOrWhiteSpace(clientId) || string.IsNullOrWhiteSpace(clientSecret))
                return false;

            return _clientCredentials.TryGetValue(clientId, out var storedSecret) && storedSecret == clientSecret;
        }

        /// <summary>
        /// 记录Token使用情况
        /// </summary>
        public async Task<bool> RecordTokenUsageAsync(TokenUsageRecord record)
        {
            try
            {
                await Task.CompletedTask; // 保持异步接口一致性
                _usageRecords.Add(record);
                _logger.LogDebug("记录Token使用情况: {UserId}, {ClientId}, {RequestType}, {Success}", 
                    record.UserId, record.ClientId, record.RequestType, record.Success);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "记录Token使用情况时发生异常");
                return false;
            }
        }

        /// <summary>
        /// 获取Token使用记录
        /// </summary>
        public async Task<ExternalTokenUsageResponse> GetTokenUsageRecordsAsync(
            string userId,
            string clientId = null,
            DateTime? startTime = null,
            DateTime? endTime = null,
            int pageNumber = 1,
            int pageSize = 20)
        {
            try
            {
                await Task.CompletedTask; // 保持异步接口一致性

                var query = _usageRecords.AsQueryable();

                // 过滤条件
                if (!string.IsNullOrEmpty(userId))
                    query = query.Where(r => r.UserId == userId);

                if (!string.IsNullOrEmpty(clientId))
                    query = query.Where(r => r.ClientId == clientId);

                if (startTime.HasValue)
                    query = query.Where(r => r.RequestTime >= startTime.Value);

                if (endTime.HasValue)
                    query = query.Where(r => r.RequestTime <= endTime.Value);

                var totalCount = query.Count();
                var records = query
                    .OrderByDescending(r => r.RequestTime)
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToArray();

                return new ExternalTokenUsageResponse
                {
                    Success = true,
                    UsageRecords = records,
                    TotalCount = totalCount,
                    PageNumber = pageNumber,
                    PageSize = pageSize
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取Token使用记录时发生异常");
                return new ExternalTokenUsageResponse
                {
                    Success = false,
                    ErrorCode = "INTERNAL_ERROR",
                    ErrorMessage = "获取使用记录失败"
                };
            }
        }

        /// <summary>
        /// 获取Token使用统计
        /// </summary>
        public async Task<TokenUsageStatistics> GetTokenUsageStatisticsAsync(string userId, string clientId = null, int days = 30)
        {
            try
            {
                await Task.CompletedTask; // 保持异步接口一致性

                var endTime = DateTime.UtcNow;
                var startTime = endTime.AddDays(-days);

                var query = _usageRecords.AsQueryable()
                    .Where(r => r.RequestTime >= startTime && r.RequestTime <= endTime);

                if (!string.IsNullOrEmpty(userId))
                    query = query.Where(r => r.UserId == userId);

                if (!string.IsNullOrEmpty(clientId))
                    query = query.Where(r => r.ClientId == clientId);

                var records = query.ToList();

                var statistics = new TokenUsageStatistics
                {
                    UserId = userId,
                    ClientId = clientId,
                    StartTime = startTime,
                    EndTime = endTime,
                    TotalRequests = records.Count,
                    SuccessfulRequests = records.Count(r => r.Success),
                    FailedRequests = records.Count(r => !r.Success),
                    RequestsByType = records.GroupBy(r => r.RequestType)
                        .ToDictionary(g => g.Key, g => g.Count()),
                    RequestsByDate = records.GroupBy(r => r.RequestTime.Date)
                        .ToDictionary(g => g.Key, g => g.Count())
                };

                return statistics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取Token使用统计时发生异常");
                return new TokenUsageStatistics
                {
                    UserId = userId,
                    ClientId = clientId,
                    StartTime = DateTime.UtcNow.AddDays(-days),
                    EndTime = DateTime.UtcNow
                };
            }
        }

        /// <summary>
        /// 获取客户端凭据列表
        /// </summary>
        public async Task<List<ClientCredential>> GetClientCredentialsAsync()
        {
            await Task.CompletedTask; // 保持异步接口一致性
            return _externalTokenConfig.ClientCredentials ?? new List<ClientCredential>();
        }

        /// <summary>
        /// 记录使用情况的私有辅助方法
        /// </summary>
        private async Task RecordUsageAsync(string userId, string clientId, ClientInfo clientInfo, 
            TokenRequestType requestType, bool success, string errorMessage, DateTime startTime)
        {
            var record = new TokenUsageRecord
            {
                UserId = userId,
                ClientId = clientId,
                ClientName = clientId, // 简化处理，实际项目应该有客户端名称映射
                IpAddress = clientInfo?.IpAddress,
                UserAgent = clientInfo?.UserAgent,
                RequestTime = startTime,
                ResponseTime = DateTime.UtcNow,
                Success = success,
                ErrorMessage = errorMessage,
                RequestType = requestType
            };

            await RecordTokenUsageAsync(record);
        }
    }
} 