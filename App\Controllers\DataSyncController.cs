using ChanjetOAuthTest.App.Interfaces;
using ChanjetOAuthTest.App.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ChanjetOAuthTest.App.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class DataSyncController : ControllerBase
    {
        private readonly IDataSyncService _dataSyncService;
        private readonly ILogger<DataSyncController> _logger;

        public DataSyncController(IDataSyncService dataSyncService, ILogger<DataSyncController> logger)
        {
            _dataSyncService = dataSyncService ?? throw new ArgumentNullException(nameof(dataSyncService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 启动数据同步任务
        /// </summary>
        [HttpPost("start")]
        public IActionResult StartSync()
        {
            try
            {
                _dataSyncService.StartApiSync();
                return Ok(new
                {
                    success = true,
                    message = "数据同步任务已启动",
                    timestamp = DateTime.Now
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "启动数据同步任务失败");
                return StatusCode(500, new
                {
                    success = false,
                    message = "启动数据同步任务失败",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// 停止数据同步任务
        /// </summary>
        [HttpPost("stop")]
        public IActionResult StopSync()
        {
            try
            {
                _dataSyncService.StopApiSync();
                return Ok(new
                {
                    success = true,
                    message = "数据同步任务已停止",
                    timestamp = DateTime.Now
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "停止数据同步任务失败");
                return StatusCode(500, new
                {
                    success = false,
                    message = "停止数据同步任务失败",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// 获取同步状态
        /// </summary>
        [HttpGet("status")]
        public IActionResult GetStatus()
        {
            try
            {
                var status = _dataSyncService.GetSyncStatus();
                return Ok(new
                {
                    success = true,
                    message = "获取同步状态成功",
                    data = status,
                    timestamp = DateTime.Now
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取同步状态失败");
                return StatusCode(500, new
                {
                    success = false,
                    message = "获取同步状态失败",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// 获取API端点配置
        /// </summary>
        [HttpGet("endpoints")]
        public IActionResult GetEndpoints()
        {
            try
            {
                var endpoints = _dataSyncService.GetApiEndpoints();
                return Ok(new
                {
                    success = true,
                    message = "获取API端点配置成功",
                    data = endpoints,
                    count = endpoints.Count,
                    timestamp = DateTime.Now
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取API端点配置失败");
                return StatusCode(500, new
                {
                    success = false,
                    message = "获取API端点配置失败",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// 更新API端点配置
        /// </summary>
        [HttpPost("endpoints")]
        public IActionResult UpdateEndpoints([FromBody] List<ApiEndpoint> endpoints)
        {
            try
            {
                if (endpoints == null)
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "端点配置不能为空"
                    });
                }

                bool result = _dataSyncService.UpdateApiEndpoints(endpoints);
                
                if (result)
                {
                    return Ok(new
                    {
                        success = true,
                        message = "API端点配置更新成功",
                        count = endpoints.Count,
                        timestamp = DateTime.Now
                    });
                }
                else
                {
                    return StatusCode(500, new
                    {
                        success = false,
                        message = "API端点配置更新失败"
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新API端点配置失败");
                return StatusCode(500, new
                {
                    success = false,
                    message = "更新API端点配置失败",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// 刷新配置
        /// </summary>
        [HttpPost("refresh-config")]
        public IActionResult RefreshConfig()
        {
            try
            {
                _dataSyncService.RefreshApiEndpoints();
                return Ok(new
                {
                    success = true,
                    message = "配置刷新成功",
                    timestamp = DateTime.Now
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新配置失败");
                return StatusCode(500, new
                {
                    success = false,
                    message = "刷新配置失败",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// 执行自定义API调用
        /// </summary>
        [HttpPost("execute/{accountIndex}")]
        public async Task<IActionResult> ExecuteCustomApi(int accountIndex, [FromBody] CustomApiRequest request)
        {
            try
            {
                if (request == null || string.IsNullOrWhiteSpace(request.Endpoint))
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "请求参数不完整"
                    });
                }

                string result = await _dataSyncService.ExecuteCustomApiCall(accountIndex, request.Endpoint, request.RequestBody ?? "{}");
                
                return Ok(new
                {
                    success = true,
                    message = "API调用执行成功",
                    data = result,
                    endpoint = request.Endpoint,
                    accountIndex = accountIndex,
                    timestamp = DateTime.Now
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行自定义API调用失败");
                return StatusCode(500, new
                {
                    success = false,
                    message = "执行自定义API调用失败",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// 验证指定账户的Token
        /// </summary>
        [HttpGet("validate-token/{accountIndex}")]
        public async Task<IActionResult> ValidateToken(int accountIndex)
        {
            try
            {
                bool isValid = await _dataSyncService.ValidateTokenAsync(accountIndex);
                
                return Ok(new
                {
                    success = true,
                    message = "Token验证完成",
                    data = new
                    {
                        accountIndex = accountIndex,
                        isValid = isValid,
                        status = isValid ? "有效" : "无效"
                    },
                    timestamp = DateTime.Now
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证Token失败");
                return StatusCode(500, new
                {
                    success = false,
                    message = "验证Token失败",
                    error = ex.Message
                });
            }
        }
    }

    /// <summary>
    /// 自定义API请求模型
    /// </summary>
    public class CustomApiRequest
    {
        public string Endpoint { get; set; }
        public string RequestBody { get; set; }
    }
} 