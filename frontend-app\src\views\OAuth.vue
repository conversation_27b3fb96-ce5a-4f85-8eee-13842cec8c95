<template>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold tracking-tight">OAuth 授权</h1>
        <p class="text-muted-foreground">管理第三方应用的OAuth授权流程</p>
      </div>
      <Button @click="startOAuthFlow">
        <Key class="mr-2 h-4 w-4" />
        开始授权流程
      </Button>
    </div>

    <div class="grid gap-6 lg:grid-cols-2">
      <Card>
        <CardHeader>
          <CardTitle class="flex items-center">
            <ShieldCheck class="mr-2 h-4 w-4 text-green-500" />
            授权流程
          </CardTitle>
          <CardDescription>第三方应用OAuth2.0授权流程</CardDescription>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="space-y-3">
            <div v-for="(step, index) in oauthSteps" :key="index" class="flex items-start space-x-3">
              <div :class="step.completed ? 'bg-green-500' : 'bg-gray-300'" class="w-6 h-6 rounded-full flex items-center justify-center text-white text-xs font-bold">
                {{ index + 1 }}
              </div>
              <div>
                <h4 class="font-medium">{{ step.title }}</h4>
                <p class="text-sm text-muted-foreground">{{ step.description }}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>系统状态</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="space-y-3">
            <div class="flex items-center justify-between">
              <span>OAuth服务</span>
              <Badge variant="default">运行中</Badge>
            </div>
            <div class="flex items-center justify-between">
              <span>授权服务</span>
              <Badge variant="default">正常</Badge>
            </div>
            <div class="flex items-center justify-between">
              <span>安全连接</span>
              <Badge variant="default">HTTPS</Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Key, ShieldCheck } from 'lucide-vue-next'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'

const oauthSteps = ref([
  { title: '客户端重定向', description: '应用重定向到授权服务', completed: true },
  { title: '用户授权', description: '用户确认授权请求', completed: true },
  { title: '授权码生成', description: '服务器生成授权码', completed: false },
  { title: '获取访问令牌', description: '使用授权码换取访问令牌', completed: false }
])

const startOAuthFlow = () => {
  console.log('开始OAuth授权流程')
}
</script>
