<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import {
  Database,
  Settings,
  Activity,
  RefreshCw,
  Shield,
  BarChart3,
  Key,
  Home,
  UserCheck,
} from 'lucide-vue-next'

import NavMain from '@/components/layout/admin/components/NavMain.vue'
import NavSecondary from '@/components/layout/admin/components/NavSecondary.vue'
import NavUser from '@/components/layout/admin/components/NavUser.vue'
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  type SidebarProps,
} from '@/components/ui/sidebar'

const props = withDefaults(defineProps<SidebarProps>(), {
  variant: 'inset',
})

const route = useRoute()

const data = {
  user: {
    name: 'Administrator',
    email: '<EMAIL>',
    avatar: '/avatars/admin.jpg',
  },
  navMain: [
    {
      title: '管理面板',
      url: '/',
      icon: Home,
      isActive: route.path === '/',
    },
    {
      title: 'OAuth授权',
      url: '/oauth',
      icon: Shield,
      isActive: route.path === '/oauth',
    },
    {
      title: 'Token管理',
      url: '/tokens',
      icon: Key,
      isActive: route.path === '/tokens',
    },
    {
      title: '数据同步',
      url: '/data-sync',
      icon: RefreshCw,
      isActive: route.path === '/data-sync',
    },
    {
      title: '系统监控',
      url: '/monitoring',
      icon: Activity,
      isActive: route.path === '/monitoring',
    },
  ],
  navSecondary: [
    {
      title: '系统设置',
      url: '/settings',
      icon: Settings,
    },
  ],
}
</script>

<template>
  <Sidebar v-bind="props">
    <SidebarHeader>
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton size="lg" as-child>
            <a href="/">
              <div class="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                <Shield class="size-4" />
              </div>
              <div class="grid flex-1 text-left text-sm leading-tight">
                <span class="truncate font-semibold">ChanjetOAuth</span>
                <span class="truncate text-xs">授权管理系统</span>
              </div>
            </a>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    </SidebarHeader>
    <SidebarContent>
      <NavMain :items="data.navMain" />
      <NavSecondary :items="data.navSecondary" class="mt-auto" />
    </SidebarContent>
    <SidebarFooter>
      <NavUser :user="data.user" />
    </SidebarFooter>
  </Sidebar>
</template>
