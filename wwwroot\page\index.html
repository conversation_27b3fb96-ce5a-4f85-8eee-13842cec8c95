<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChanjetOAuth 管理系统</title>
    <!-- 引入Tailwind CSS -->
    <script src="/src/js/tailwindcss.js"></script>
    <!-- 引入Lucide图标库 -->
    <script src="/src/js/lucide.min.js"></script>
    <!-- 引入Vue.js -->
    <script src="/src/js/vue.global.js"></script>
    <style>
        /* 自定义全局样式和字体 */
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        }
        
        /* 美化滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        /* 卡片悬停动画 */
        .feature-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .feature-card:hover {
            transform: translateY(-8px) scale(1.02);
        }

        /* 加载动画 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.6s ease-out forwards;
        }

        /* 渐变背景动画 */
        .gradient-bg {
            background: linear-gradient(-45deg, #667eea, #764ba2, #f093fb, #f5576c);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
    </style>
</head>
<body class="gradient-bg min-h-screen">
    <div id="app">
        <!-- 主容器 -->
        <div class="min-h-screen flex items-center justify-center p-4">
            <div class="max-w-7xl w-full bg-white/95 backdrop-blur-lg rounded-3xl shadow-2xl p-8 lg:p-12">
                
                <!-- 页面头部 -->
                <header class="text-center mb-12" :class="{ 'fade-in-up': loaded }">
                    <div class="flex items-center justify-center mb-6">
                        <div class="bg-gradient-to-r from-blue-600 to-purple-600 p-4 rounded-2xl mr-4">
                            <i data-lucide="shield-check" class="h-12 w-12 text-white"></i>
                        </div>
                        <div>
                            <h1 class="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                                ChanjetOAuth
                            </h1>
                            <p class="text-lg text-gray-600 mt-1">管理系统</p>
                        </div>
                    </div>
                    <p class="text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed">
                        集成OAuth认证、Token管理、数据同步于一体的综合管理平台
                    </p>
                    
                    <!-- 系统状态指示器 -->
                    <div class="mt-6 flex items-center justify-center space-x-6">
                        <div class="flex items-center space-x-2">
                            <div :class="systemStatus.tokenService.class" class="h-3 w-3 rounded-full"></div>
                            <span class="text-sm font-medium text-gray-700">Token服务</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div :class="systemStatus.syncService.class" class="h-3 w-3 rounded-full"></div>
                            <span class="text-sm font-medium text-gray-700">数据同步</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div :class="systemStatus.oauth.class" class="h-3 w-3 rounded-full"></div>
                            <span class="text-sm font-medium text-gray-700">OAuth服务</span>
                        </div>
                    </div>
                </header>

                <!-- 功能卡片网格 -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                    <div 
                        v-for="(feature, index) in features" 
                        :key="feature.id"
                        :class="{ 'fade-in-up': loaded }"
                        :style="{ animationDelay: `${index * 150}ms` }"
                        class="feature-card bg-white rounded-2xl p-8 shadow-lg border border-gray-100 cursor-pointer group"
                        @click="navigateToFeature(feature)"
                    >
                        <div class="flex items-center justify-between mb-6">
                            <div :class="feature.iconBg" class="p-4 rounded-xl group-hover:scale-110 transition-transform duration-300">
                                <i :data-lucide="feature.icon" class="h-8 w-8 text-white"></i>
                            </div>
                            <span 
                                :class="feature.statusClass" 
                                class="px-3 py-1 rounded-full text-xs font-semibold"
                            >
                                {{ feature.status }}
                            </span>
                        </div>
                        
                        <h3 class="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">
                            {{ feature.title }}
                        </h3>
                        <p class="text-gray-600 leading-relaxed mb-4">
                            {{ feature.description }}
                        </p>
                        
                        <div class="flex items-center text-blue-600 group-hover:text-purple-600 transition-colors">
                            <span class="text-sm font-medium">立即访问</span>
                            <i data-lucide="arrow-right" class="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform"></i>
                        </div>
                    </div>
                </div>

                <!-- 快速操作区域 -->
                <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8" :class="{ 'fade-in-up': loaded }" style="animation-delay: 600ms">
                    <div class="text-center mb-8">
                        <h3 class="text-2xl font-bold text-gray-900 mb-2 flex items-center justify-center">
                            <i data-lucide="zap" class="h-6 w-6 mr-2 text-yellow-500"></i>
                            快速操作
                        </h3>
                        <p class="text-gray-600">一键访问常用功能</p>
                    </div>
                    
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                        <button 
                            v-for="action in quickActions"
                            :key="action.id"
                            @click="executeQuickAction(action)"
                            :class="action.buttonClass"
                            class="flex items-center justify-center px-6 py-4 rounded-xl font-semibold transition-all duration-300 hover:shadow-lg hover:-translate-y-1"
                        >
                            <i :data-lucide="action.icon" class="h-5 w-5 mr-2"></i>
                            {{ action.label }}
                        </button>
                    </div>
                </div>

                <!-- 系统信息 -->
                <div class="mt-12 bg-blue-50 rounded-2xl p-8" :class="{ 'fade-in-up': loaded }" style="animation-delay: 800ms">
                    <div class="flex items-start">
                        <div class="bg-blue-100 p-3 rounded-xl mr-4">
                            <i data-lucide="info" class="h-6 w-6 text-blue-600"></i>
                        </div>
                        <div class="flex-1">
                            <h4 class="text-lg font-bold text-blue-900 mb-4">系统功能说明</h4>
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <div v-for="info in systemInfo" :key="info.title" class="flex items-start">
                                    <div class="bg-blue-200 p-2 rounded-lg mr-3 mt-1">
                                        <i :data-lucide="info.icon" class="h-4 w-4 text-blue-700"></i>
                                    </div>
                                    <div>
                                        <h5 class="font-semibold text-blue-900 mb-1">{{ info.title }}</h5>
                                        <p class="text-blue-700 text-sm leading-relaxed">{{ info.description }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 通知消息 -->
        <div 
            v-if="notification.show" 
            class="fixed top-6 right-6 bg-white border border-gray-200 rounded-xl shadow-lg p-4 max-w-sm z-50 transition-all duration-300"
            :class="notification.type === 'success' ? 'border-green-200' : notification.type === 'error' ? 'border-red-200' : 'border-blue-200'"
        >
            <div class="flex items-start">
                <div :class="notification.type === 'success' ? 'text-green-500' : notification.type === 'error' ? 'text-red-500' : 'text-blue-500'" class="mr-3 mt-0.5">
                    <i :data-lucide="notification.icon" class="h-5 w-5"></i>
                </div>
                <div class="flex-1">
                    <p class="font-medium text-gray-900">{{ notification.title }}</p>
                    <p class="text-sm text-gray-600 mt-1">{{ notification.message }}</p>
                </div>
                <button @click="hideNotification" class="text-gray-400 hover:text-gray-600 ml-2">
                    <i data-lucide="x" class="h-4 w-4"></i>
                </button>
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref, onMounted, reactive } = Vue;

        createApp({
            setup() {
                const loaded = ref(false);
                const systemStatus = reactive({
                    tokenService: { class: 'bg-yellow-400 animate-pulse' },
                    syncService: { class: 'bg-yellow-400 animate-pulse' },
                    oauth: { class: 'bg-yellow-400 animate-pulse' }
                });

                const notification = reactive({
                    show: false,
                    type: 'info',
                    title: '',
                    message: '',
                    icon: 'info'
                });

                const features = ref([
                    {
                        id: 'oauth',
                        title: 'OAuth授权',
                        description: '畅捷通OAuth2.0授权流程，安全获取访问令牌，支持自动跳转和回调处理。',
                        icon: 'key',
                        iconBg: 'bg-gradient-to-r from-blue-500 to-blue-600',
                        status: '已就绪',
                        statusClass: 'bg-green-100 text-green-800',
                        url: '/oauth/page'
                    },
                    {
                        id: 'token',
                        title: 'Token管理',
                        description: '全面的Token生命周期管理，包括获取、刷新、状态监控和自动过期处理。',
                        icon: 'activity',
                        iconBg: 'bg-gradient-to-r from-green-500 to-green-600',
                        status: '已就绪',
                        statusClass: 'bg-green-100 text-green-800',
                        url: '/page/dashboard.html'
                    },
                    {
                        id: 'sync',
                        title: '数据同步',
                        description: '自动化数据同步服务，支持多账户、多端点配置，实时监控同步状态。',
                        icon: 'refresh-cw',
                        iconBg: 'bg-gradient-to-r from-purple-500 to-purple-600',
                        status: '新功能',
                        statusClass: 'bg-yellow-100 text-yellow-800',
                        url: '/page/data-sync.html'
                    },
                    {
                        id: 'monitor',
                        title: '系统监控',
                        description: '实时系统状态监控，性能指标分析，日志管理和告警通知。',
                        icon: 'monitor',
                        iconBg: 'bg-gradient-to-r from-orange-500 to-orange-600',
                        status: '即将推出',
                        statusClass: 'bg-gray-100 text-gray-600',
                        url: '#'
                    },
                    {
                        id: 'users',
                        title: '用户管理',
                        description: '多用户权限管理，角色分配，操作审计和安全策略配置。',
                        icon: 'users',
                        iconBg: 'bg-gradient-to-r from-indigo-500 to-indigo-600',
                        status: '即将推出',
                        statusClass: 'bg-gray-100 text-gray-600',
                        url: '#'
                    },
                    {
                        id: 'api',
                        title: 'API网关',
                        description: '统一API入口，请求路由，限流控制，接口文档和测试工具。',
                        icon: 'globe',
                        iconBg: 'bg-gradient-to-r from-pink-500 to-pink-600',
                        status: '即将推出',
                        statusClass: 'bg-gray-100 text-gray-600',
                        url: '#'
                    }
                ]);

                const quickActions = ref([
                    {
                        id: 'authorize',
                        label: '立即授权',
                        icon: 'play',
                        buttonClass: 'bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700',
                        url: '/oauth/authorize'
                    },
                    {
                        id: 'status',
                        label: '查看状态',
                        icon: 'eye',
                        buttonClass: 'bg-gradient-to-r from-green-500 to-green-600 text-white hover:from-green-600 hover:to-green-700',
                        action: 'checkStatus'
                    },
                    {
                        id: 'sync',
                        label: '启动同步',
                        icon: 'play-circle',
                        buttonClass: 'bg-gradient-to-r from-purple-500 to-purple-600 text-white hover:from-purple-600 hover:to-purple-700',
                        url: '/page/data-sync.html'
                    },
                    {
                        id: 'docs',
                        label: '使用文档',
                        icon: 'book-open',
                        buttonClass: 'bg-gradient-to-r from-gray-500 to-gray-600 text-white hover:from-gray-600 hover:to-gray-700',
                        action: 'showDocs'
                    }
                ]);

                const systemInfo = ref([
                    {
                        title: 'OAuth授权',
                        description: '支持畅捷通标准OAuth2.0流程，自动处理授权码交换',
                        icon: 'shield'
                    },
                    {
                        title: 'Token管理',
                        description: '智能Token生命周期管理，支持自动刷新和过期提醒',
                        icon: 'clock'
                    },
                    {
                        title: '数据同步',
                        description: '多账户数据同步，支持自定义API端点和调度策略',
                        icon: 'database'
                    },
                    {
                        title: '安全机制',
                        description: 'RSA加密传输，Token安全存储，操作日志记录',
                        icon: 'lock'
                    }
                ]);

                // 方法
                const showNotification = (type, title, message) => {
                    notification.show = true;
                    notification.type = type;
                    notification.title = title;
                    notification.message = message;
                    notification.icon = type === 'success' ? 'check-circle' : type === 'error' ? 'alert-circle' : 'info';
                    
                    setTimeout(() => {
                        hideNotification();
                    }, 5000);
                };

                const hideNotification = () => {
                    notification.show = false;
                };

                const navigateToFeature = (feature) => {
                    if (feature.url === '#') {
                        showNotification('info', '功能即将推出', `${feature.title}功能正在开发中，敬请期待！`);
                    } else {
                        window.location.href = feature.url;
                    }
                };

                const executeQuickAction = (action) => {
                    if (action.url) {
                        window.location.href = action.url;
                    } else if (action.action === 'checkStatus') {
                        checkSystemStatus();
                    } else if (action.action === 'showDocs') {
                        showNotification('info', '文档说明', '详细使用文档正在整理中，可通过各功能页面了解具体操作步骤。');
                    }
                };

                const checkSystemStatus = async () => {
                    showNotification('info', '正在检查', '系统状态检查中...');
                    
                    try {
                        // 检查Token服务
                        const tokenResponse = await fetch('/api/token/status?userId=default');
                        systemStatus.tokenService.class = tokenResponse.ok ? 'bg-green-500' : 'bg-red-500';
                        
                        // 检查数据同步服务
                        const syncResponse = await fetch('/api/datasync/status');
                        systemStatus.syncService.class = syncResponse.ok ? 'bg-green-500' : 'bg-red-500';
                        
                        // OAuth服务（假设可用）
                        systemStatus.oauth.class = 'bg-green-500';
                        
                        hideNotification();
                        showNotification('success', '检查完成', '系统状态检查完成，请查看状态指示器。');
                    } catch (error) {
                        systemStatus.tokenService.class = 'bg-red-500';
                        systemStatus.syncService.class = 'bg-red-500';
                        systemStatus.oauth.class = 'bg-red-500';
                        
                        hideNotification();
                        showNotification('error', '检查失败', '系统状态检查失败，请检查服务是否正常运行。');
                    }
                };

                // 生命周期
                onMounted(() => {
                    // 初始化Lucide图标
                    lucide.createIcons();
                    
                    // 页面加载动画
                    setTimeout(() => {
                        loaded.value = true;
                    }, 100);
                    
                    // 自动检查系统状态
                    setTimeout(() => {
                        checkSystemStatus();
                    }, 1000);
                });

                return {
                    loaded,
                    systemStatus,
                    notification,
                    features,
                    quickActions,
                    systemInfo,
                    navigateToFeature,
                    executeQuickAction,
                    hideNotification
                };
            }
        }).mount('#app');
    </script>
</body>
</html> 