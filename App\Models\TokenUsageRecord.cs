using System;
using System.Text.Json.Serialization;

namespace ChanjetOAuthTest.App.Models
{
    /// <summary>
    /// Token使用记录模型
    /// </summary>
    public class TokenUsageRecord
    {
        /// <summary>
        /// 记录ID
        /// </summary>
        [JsonPropertyName("id")]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 用户ID
        /// </summary>
        [JsonPropertyName("user_id")]
        public string UserId { get; set; }

        /// <summary>
        /// 请求方应用ID或标识
        /// </summary>
        [JsonPropertyName("client_id")]
        public string ClientId { get; set; }

        /// <summary>
        /// 请求方应用名称
        /// </summary>
        [JsonPropertyName("client_name")]
        public string ClientName { get; set; }

        /// <summary>
        /// 请求的IP地址
        /// </summary>
        [JsonPropertyName("ip_address")]
        public string IpAddress { get; set; }

        /// <summary>
        /// 用户代理信息
        /// </summary>
        [JsonPropertyName("user_agent")]
        public string UserAgent { get; set; }

        /// <summary>
        /// 请求时间
        /// </summary>
        [JsonPropertyName("request_time")]
        public DateTime RequestTime { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 响应时间
        /// </summary>
        [JsonPropertyName("response_time")]
        public DateTime? ResponseTime { get; set; }

        /// <summary>
        /// 请求是否成功
        /// </summary>
        [JsonPropertyName("success")]
        public bool Success { get; set; }

        /// <summary>
        /// 错误信息（如果请求失败）
        /// </summary>
        [JsonPropertyName("error_message")]
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 请求类型（获取、刷新、验证等）
        /// </summary>
        [JsonPropertyName("request_type")]
        public TokenRequestType RequestType { get; set; }

        /// <summary>
        /// 请求的作用域
        /// </summary>
        [JsonPropertyName("scope")]
        public string Scope { get; set; }

        /// <summary>
        /// 额外的请求信息
        /// </summary>
        [JsonPropertyName("additional_info")]
        public string AdditionalInfo { get; set; }
    }

    /// <summary>
    /// Token请求类型枚举
    /// </summary>
    public enum TokenRequestType
    {
        /// <summary>
        /// 获取新Token
        /// </summary>
        Acquire,

        /// <summary>
        /// 刷新Token
        /// </summary>
        Refresh,

        /// <summary>
        /// 验证Token
        /// </summary>
        Validate,

        /// <summary>
        /// 撤销Token
        /// </summary>
        Revoke
    }
} 