import { ref, computed, watch } from 'vue'
import { mainNavigation, type NavigationItem } from '@/config/navigation'

export interface SearchResult {
  type: 'navigation' | 'content' | 'setting'
  title: string
  description: string
  href?: string
  action?: () => void
  icon?: any
}

export const useSearch = () => {
  const searchQuery = ref('')
  const isSearchOpen = ref(false)
  const searchResults = ref<SearchResult[]>([])
  const isSearching = ref(false)

  // 搜索导航
  const searchNavigation = (query: string): SearchResult[] => {
    const lowerQuery = query.toLowerCase()
    return mainNavigation
      .filter(item => 
        item.title.toLowerCase().includes(lowerQuery) ||
        item.description?.toLowerCase().includes(lowerQuery)
      )
      .map(item => ({
        type: 'navigation' as const,
        title: item.title,
        description: item.description || '',
        href: item.href,
        icon: item.icon
      }))
  }

  // 搜索内容项（可扩展）
  const searchContent = (query: string): SearchResult[] => {
    // 这里可以添加搜索其他内容的逻辑
    // 比如搜索应用、用户、日志等
    const mockResults: SearchResult[] = [
      {
        type: 'content',
        title: '演示应用',
        description: '用于测试的OAuth应用',
        href: '/oauth/apps/demo'
      },
      {
        type: 'setting',
        title: '刷新令牌设置',
        description: '配置刷新令牌的有效期',
        href: '/settings/tokens'
      }
    ]

    const lowerQuery = query.toLowerCase()
    return mockResults.filter(item =>
      item.title.toLowerCase().includes(lowerQuery) ||
      item.description.toLowerCase().includes(lowerQuery)
    )
  }

  // 执行搜索
  const performSearch = async (query: string) => {
    if (!query.trim()) {
      searchResults.value = []
      return
    }

    isSearching.value = true
    
    try {
      // 模拟异步搜索
      await new Promise(resolve => setTimeout(resolve, 200))
      
      const navigationResults = searchNavigation(query)
      const contentResults = searchContent(query)
      
      searchResults.value = [...navigationResults, ...contentResults]
    } catch (error) {
      console.error('搜索失败:', error)
      searchResults.value = []
    } finally {
      isSearching.value = false
    }
  }

  // 监听搜索查询变化
  let debounceTimer: number | undefined
  watch(searchQuery, (newQuery) => {
    if (debounceTimer) {
      clearTimeout(debounceTimer)
    }
    debounceTimer = setTimeout(() => {
      performSearch(newQuery)
    }, 300)
  })

  // 打开搜索
  const openSearch = () => {
    isSearchOpen.value = true
  }

  // 关闭搜索
  const closeSearch = () => {
    isSearchOpen.value = false
    searchQuery.value = ''
  }

  // 选择搜索结果
  const selectResult = (result: SearchResult) => {
    if (result.href) {
      // 导航到目标页面
      window.location.href = result.href
    } else if (result.action) {
      // 执行自定义操作
      result.action()
    }
    closeSearch()
  }

  // 快捷键处理
  const handleKeydown = (event: KeyboardEvent) => {
    // Ctrl/Cmd + K 打开搜索
    if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
      event.preventDefault()
      openSearch()
    }
    // ESC 关闭搜索
    if (event.key === 'Escape' && isSearchOpen.value) {
      closeSearch()
    }
  }

  return {
    searchQuery,
    isSearchOpen: computed(() => isSearchOpen.value),
    searchResults: computed(() => searchResults.value),
    isSearching: computed(() => isSearching.value),
    openSearch,
    closeSearch,
    selectResult,
    handleKeydown
  }
} 
