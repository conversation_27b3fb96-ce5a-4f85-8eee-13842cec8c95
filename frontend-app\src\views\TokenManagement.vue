<template>
  <div class="space-y-8">
    <!-- 页面标题 -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div class="space-y-1">
        <h1 class="text-3xl font-bold tracking-tight text-slate-900">Token 管理</h1>
        <p class="text-slate-600">管理和监控OAuth访问令牌的状态和生命周期</p>
      </div>
      <div class="flex items-center gap-3">
        <Button @click="refreshTokens" :disabled="loading" variant="outline" class="shadow-sm">
          <RefreshCw class="mr-2 h-4 w-4" :class="{ 'animate-spin': loading }" />
          刷新状态
        </Button>
        <Dialog v-model:open="showCreateDialog">
          <DialogTrigger as-child>
            <Button class="shadow-sm bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700">
              <Plus class="mr-2 h-4 w-4" />
              获取新Token
            </Button>
          </DialogTrigger>
          <DialogContent class="sm:max-w-md">
            <DialogHeader>
              <DialogTitle class="text-slate-900">获取新的访问令牌</DialogTitle>
              <DialogDescription class="text-slate-600">
                输入授权码以获取有效6天的访问令牌
              </DialogDescription>
            </DialogHeader>
            <div class="space-y-4">
              <div class="space-y-2">
                <Label for="authCode" class="text-slate-700">授权码</Label>
                <Input
                  id="authCode"
                  v-model="authCodeForm.code"
                  placeholder="输入10分钟内有效的授权码"
                  class="border-slate-200 focus:border-blue-500 focus:ring-blue-500"
                  @keyup.enter="getTokenByAuthCode"
                />
                <p class="text-xs text-slate-500">
                  授权码仅能使用一次，请确保在有效期内使用
                </p>
              </div>
              <div class="space-y-2">
                <Label for="clientSelect" class="text-slate-700">选择客户</Label>
                <select
                  id="clientSelect"
                  v-model="authCodeForm.clientId"
                  class="flex h-10 w-full rounded-md border border-slate-200 bg-white px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
                >
                  <option value="">选择客户</option>
                  <option v-for="client in clients" :key="client.id" :value="client.id">
                    {{ client.name }}
                  </option>
                </select>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" @click="showCreateDialog = false">取消</Button>
              <Button @click="getTokenByAuthCode" :disabled="!authCodeForm.code.trim() || !authCodeForm.clientId" class="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700">
                获取Token
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="grid gap-6 sm:grid-cols-2 lg:grid-cols-4">
      <Card class="bg-white/60 backdrop-blur-sm border-slate-200/60 shadow-lg shadow-slate-900/5 hover:shadow-xl hover:shadow-slate-900/10 transition-all duration-200">
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-3">
          <CardTitle class="text-sm font-medium text-slate-700">活跃Token</CardTitle>
          <div class="p-2 bg-emerald-100 rounded-lg">
            <Activity class="h-4 w-4 text-emerald-600" />
          </div>
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold text-slate-900">{{ tokenStats.active }}</div>
          <p class="text-xs text-slate-500 mt-1">正在使用的令牌</p>
        </CardContent>
      </Card>

      <Card class="bg-white/60 backdrop-blur-sm border-slate-200/60 shadow-lg shadow-slate-900/5 hover:shadow-xl hover:shadow-slate-900/10 transition-all duration-200">
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-3">
          <CardTitle class="text-sm font-medium text-slate-700">即将过期</CardTitle>
          <div class="p-2 bg-amber-100 rounded-lg">
            <AlertTriangle class="h-4 w-4 text-amber-600" />
          </div>
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold text-amber-600">{{ tokenStats.expiring }}</div>
          <p class="text-xs text-slate-500 mt-1">24小时内过期</p>
        </CardContent>
      </Card>

      <Card class="bg-white/60 backdrop-blur-sm border-slate-200/60 shadow-lg shadow-slate-900/5 hover:shadow-xl hover:shadow-slate-900/10 transition-all duration-200">
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-3">
          <CardTitle class="text-sm font-medium text-slate-700">已过期</CardTitle>
          <div class="p-2 bg-red-100 rounded-lg">
            <XCircle class="h-4 w-4 text-red-600" />
          </div>
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold text-red-600">{{ tokenStats.expired }}</div>
          <p class="text-xs text-slate-500 mt-1">需要重新获取</p>
        </CardContent>
      </Card>

      <Card class="bg-white/60 backdrop-blur-sm border-slate-200/60 shadow-lg shadow-slate-900/5 hover:shadow-xl hover:shadow-slate-900/10 transition-all duration-200">
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-3">
          <CardTitle class="text-sm font-medium text-slate-700">自动刷新</CardTitle>
          <div class="p-2 bg-blue-100 rounded-lg">
            <RotateCcw class="h-4 w-4 text-blue-600" />
          </div>
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold text-blue-600">{{ tokenStats.autoRefresh }}</div>
          <p class="text-xs text-slate-500 mt-1">已启用自动刷新</p>
        </CardContent>
      </Card>
    </div>

    <!-- Token 列表和操�?-->
    <div class="grid gap-8 lg:grid-cols-3">
      <div class="lg:col-span-2">
        <Card class="bg-white/60 backdrop-blur-sm border-slate-200/60 shadow-lg shadow-slate-900/5">
          <CardHeader class="border-b border-slate-100 bg-gradient-to-r from-slate-50/50 to-blue-50/50">
            <CardTitle class="text-slate-900">Token 列表</CardTitle>
            <CardDescription class="text-slate-600">当前系统中的所有访问令牌</CardDescription>
          </CardHeader>
          <CardContent class="p-6">
            <div class="space-y-4">
              <div v-if="tokens.length === 0" class="text-center py-12">
                <div class="p-4 bg-slate-100 rounded-full w-fit mx-auto mb-4">
                  <KeyRound class="h-8 w-8 text-slate-400" />
                </div>
                <h3 class="text-lg font-semibold text-slate-900 mb-2">暂无Token</h3>
                <p class="text-slate-500">点击上方按钮获取新的访问令牌</p>
              </div>
              
              <div v-else class="space-y-4">
                <div
                  v-for="token in tokens"
                  :key="token.id"
                  class="flex items-center justify-between p-4 bg-white/80 border border-slate-200/60 rounded-xl hover:bg-white hover:shadow-md transition-all duration-200"
                >
                  <div class="flex items-center space-x-4">
                    <div :class="getTokenStatusColor(token.status)" class="p-3 rounded-xl shadow-lg">
                      <component :is="getTokenIcon(token.status)" class="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h4 class="font-semibold text-slate-900">{{ token.clientName }}</h4>
                      <p class="text-sm text-slate-600">{{ token.scope }}</p>
                      <p class="text-xs text-slate-500 mt-1">
                        过期时间: {{ formatDate(token.expiresAt) }}
                      </p>
                    </div>
                  </div>
                  <div class="flex items-center space-x-3">
                    <Badge :variant="getBadgeVariant(token.status)" class="font-medium">
                      {{ token.status }}
                    </Badge>
                    <DropdownMenu>
                      <DropdownMenuTrigger as-child>
                        <Button variant="ghost" size="sm" class="hover:bg-slate-100 rounded-lg">
                          <MoreVertical class="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent class="w-48 bg-white/95 backdrop-blur-xl border border-slate-200/60 shadow-xl shadow-slate-900/10">
                        <DropdownMenuItem @click="refreshToken(token)" class="hover:bg-slate-50">
                          <RefreshCw class="mr-2 h-4 w-4 text-slate-500" />
                          刷新Token
                        </DropdownMenuItem>
                        <DropdownMenuItem @click="viewTokenDetails(token)" class="hover:bg-slate-50">
                          <Eye class="mr-2 h-4 w-4 text-slate-500" />
                          查看详情
                        </DropdownMenuItem>
                        <DropdownMenuItem @click="copyToken(token)" class="text-blue-600 hover:bg-blue-50">
                          <Copy class="mr-2 h-4 w-4 text-blue-500" />
                          复制Token
                        </DropdownMenuItem>
                        <DropdownMenuSeparator class="bg-slate-200/60" />
                        <DropdownMenuItem @click="revokeToken(token)" class="text-red-600 hover:bg-red-50">
                          <Trash2 class="mr-2 h-4 w-4 text-red-500" />
                          撤销Token
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- 操作面板 -->
      <div class="space-y-6">
        <!-- 快速操�?-->
        <Card class="bg-white/60 backdrop-blur-sm border-slate-200/60 shadow-lg shadow-slate-900/5">
          <CardHeader class="border-b border-slate-100 bg-gradient-to-r from-amber-50/50 to-yellow-50/50">
            <CardTitle class="flex items-center text-slate-900">
              <div class="p-2 bg-amber-100 rounded-lg mr-3">
                <Zap class="h-4 w-4 text-amber-600" />
              </div>
              快速操作
            </CardTitle>
          </CardHeader>
          <CardContent class="p-4 space-y-3">
            <Button variant="outline" class="w-full justify-start hover:bg-slate-50" @click="refreshAllTokens">
              <RefreshCw class="mr-2 h-4 w-4 text-slate-500" />
              刷新所有Token
            </Button>
            <Button variant="outline" class="w-full justify-start hover:bg-slate-50" @click="exportTokens">
              <Download class="mr-2 h-4 w-4 text-slate-500" />
              导出Token列表
            </Button>
            <Button variant="outline" class="w-full justify-start hover:bg-slate-50" @click="toggleAutoRefresh">
              <RotateCcw class="mr-2 h-4 w-4 text-slate-500" />
              {{ autoRefreshEnabled ? '禁用' : '启用' }}自动刷新
            </Button>
          </CardContent>
        </Card>

        <!-- 系统状�?-->
        <Card class="bg-white/60 backdrop-blur-sm border-slate-200/60 shadow-lg shadow-slate-900/5">
          <CardHeader class="border-b border-slate-100 bg-gradient-to-r from-blue-50/50 to-indigo-50/50">
            <CardTitle class="flex items-center text-slate-900">
              <div class="p-2 bg-blue-100 rounded-lg mr-3">
                <Monitor class="h-4 w-4 text-blue-600" />
              </div>
              系统状态
            </CardTitle>
          </CardHeader>
          <CardContent class="p-4 space-y-4">
            <div class="flex items-center justify-between py-2">
              <span class="text-sm text-slate-600">OAuth服务</span>
              <Badge variant="default" class="bg-emerald-100 text-emerald-700 border-emerald-200">正常</Badge>
            </div>
            <div class="flex items-center justify-between py-2">
              <span class="text-sm text-slate-600">Token服务</span>
              <Badge variant="default" class="bg-emerald-100 text-emerald-700 border-emerald-200">正常</Badge>
            </div>
            <div class="flex items-center justify-between py-2">
              <span class="text-sm text-slate-600">自动刷新</span>
              <Badge :variant="autoRefreshEnabled ? 'default' : 'secondary'" :class="autoRefreshEnabled ? 'bg-blue-100 text-blue-700 border-blue-200' : 'bg-slate-100 text-slate-700 border-slate-200'">
                {{ autoRefreshEnabled ? '已启用' : '已禁用' }}
              </Badge>
            </div>
            <Separator class="bg-slate-200/60" />
            <div class="space-y-3">
              <div class="flex justify-between text-sm">
                <span class="text-slate-600">下次刷新</span>
                <span class="text-slate-800 font-medium">{{ nextRefreshTime }}</span>
              </div>
              <div class="flex justify-between text-sm">
                <span class="text-slate-600">上次同步</span>
                <span class="text-slate-800 font-medium">{{ lastSyncTime }}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  RefreshCw,
  Plus,
  Activity,
  AlertTriangle,
  XCircle,
  RotateCcw,
  KeyRound,
  MoreVertical,
  Eye,
  Copy,
  Trash2,
  Zap,
  Monitor,
  Download,
  CheckCircle,
  Clock
} from 'lucide-vue-next'

// UI 组件
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'

// 数据和状�?
const loading = ref(false)
const showCreateDialog = ref(false)
const autoRefreshEnabled = ref(true)

// 表单数据
const authCodeForm = ref({
  code: '',
  clientId: ''
})

// 模拟数据
const clients = [
  { id: '1', name: '用户管理系统' },
  { id: '2', name: '数据分析平台' },
  { id: '3', name: '移动应用' }
]

const tokens = ref([
  {
    id: '1',
    clientName: '用户管理系统',
    scope: 'read write',
    status: '活跃',
    expiresAt: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // 5天后
    accessToken: 'abc123...',
    refreshToken: 'def456...'
  },
  {
    id: '2',
    clientName: '数据分析平台',
    scope: 'read',
    status: '即将过期',
    expiresAt: new Date(Date.now() + 12 * 60 * 60 * 1000), // 12小时
    accessToken: 'ghi789...',
    refreshToken: 'jkl012...'
  },
  {
    id: '3',
    clientName: '移动应用',
    scope: 'read write delete',
    status: '已过期',
    expiresAt: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1天前
    accessToken: 'mno345...',
    refreshToken: 'pqr678...'
  }
])

// 计算属�?
const tokenStats = computed(() => ({
  active: tokens.value.filter(t => t.status === '活跃').length,
  expiring: tokens.value.filter(t => t.status === '即将过期').length,
  expired: tokens.value.filter(t => t.status === '已过期').length,
  autoRefresh: tokens.value.filter(t => t.status !== '已过期').length
}))

const nextRefreshTime = computed(() => {
  return new Date(Date.now() + 2 * 60 * 60 * 1000).toLocaleString()
})

const lastSyncTime = computed(() => {
  return new Date(Date.now() - 15 * 60 * 1000).toLocaleString()
})

// 工具函数
const formatDate = (date: Date) => {
  return date.toLocaleString()
}

const getTokenStatusColor = (status: string) => {
  switch (status) {
    case '活跃': return 'bg-green-500'
    case '即将过期': return 'bg-orange-500'
    case '已过期': return 'bg-red-500'
    default: return 'bg-gray-500'
  }
}

const getTokenIcon = (status: string) => {
  switch (status) {
    case '活跃': return CheckCircle
    case '即将过期': return Clock
    case '已过期': return XCircle
    default: return KeyRound
  }
}

const getBadgeVariant = (status: string) => {
  switch (status) {
    case '活跃': return 'default'
    case '即将过期': return 'secondary'
    case '已过期': return 'destructive'
    default: return 'outline'
  }
}

// 方法
const refreshTokens = async () => {
  loading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    // 这里会调用实际的API
    console.log('刷新Token列表')
  } finally {
    loading.value = false
  }
}

const getTokenByAuthCode = async () => {
  if (!authCodeForm.value.code.trim() || !authCodeForm.value.clientId) return
  
  try {
    // 调用API获取Token
    console.log('通过授权码获取Token:', authCodeForm.value)
    showCreateDialog.value = false
    authCodeForm.value = { code: '', clientId: '' }
    await refreshTokens()
  } catch (error) {
    console.error('获取Token失败:', error)
  }
}

const refreshToken = async (token: any) => {
  console.log('刷新Token:', token.id)
}

const viewTokenDetails = (token: any) => {
  console.log('查看Token详情:', token)
}

const copyToken = async (token: any) => {
  try {
    await navigator.clipboard.writeText(token.accessToken)
    console.log('Token已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
  }
}

const revokeToken = (token: any) => {
  console.log('撤销Token:', token.id)
}

const refreshAllTokens = () => {
  console.log('刷新所有Token')
}

const exportTokens = () => {
  console.log('导出Token列表')
}

const toggleAutoRefresh = () => {
  autoRefreshEnabled.value = !autoRefreshEnabled.value
  console.log('自动刷新状态:', autoRefreshEnabled.value ? '已启用' : '已禁用')
}

onMounted(() => {
  refreshTokens()
})
</script>
