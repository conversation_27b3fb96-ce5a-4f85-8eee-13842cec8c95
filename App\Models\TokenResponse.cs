using System;
using System.Text.Json.Serialization;

namespace ChanjetOAuthTest.App.Models
{
    /// <summary>
    /// GetToken API 的响应模型
    /// </summary>
    public class TokenResponse
    {
        [JsonPropertyName("success")]
        public bool Success { get; set; }

        [JsonPropertyName("message")]
        public string Message { get; set; }

        [JsonPropertyName("data")]
        public TokenData Data { get; set; }

        [JsonPropertyName("tips")]
        public TokenTips Tips { get; set; }

        [JsonPropertyName("error_code")]
        public string ErrorCode { get; set; }

        [JsonPropertyName("suggestion")]
        public string Suggestion { get; set; }
    }

    /// <summary>
    /// Token数据
    /// </summary>
    public class TokenData
    {
        [JsonPropertyName("access_token")]
        public string AccessToken { get; set; }

        [JsonPropertyName("refresh_token")]
        public string RefreshToken { get; set; }

        [JsonPropertyName("expires_in")]
        public int ExpiresIn { get; set; }

        [JsonPropertyName("refresh_expires_in")]
        public int RefreshExpiresIn { get; set; }

        [JsonPropertyName("token_type")]
        public string TokenType { get; set; }

        [JsonPropertyName("scope")]
        public string Scope { get; set; }

        [JsonPropertyName("expires_at")]
        public DateTime ExpiresAt { get; set; }

        [JsonPropertyName("refresh_expires_at")]
        public DateTime RefreshExpiresAt { get; set; }

        [JsonPropertyName("user_id")]
        public string UserId { get; set; }

        [JsonPropertyName("user_auth_permanent_code")]
        public string UserAuthPermanentCode { get; set; }
    }

    /// <summary>
    /// Token使用提示
    /// </summary>
    public class TokenTips
    {
        [JsonPropertyName("access_token_desc")]
        public string AccessTokenDesc { get; set; }

        [JsonPropertyName("refresh_token_desc")]
        public string RefreshTokenDesc { get; set; }

        [JsonPropertyName("user_auth_permanent_code_desc")]
        public string UserAuthPermanentCodeDesc { get; set; }

        [JsonPropertyName("suggestion")]
        public string Suggestion { get; set; }
    }
} 