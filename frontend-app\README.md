# Vue 3 + Vite 现代化前端应用

这是一个基于 Vue 3 + Vite 的现代化前端应用项目，集成了以下技术栈：

## 🚀 技术栈

- **Vue 3** - 渐进式 JavaScript 框架，使用 Composition API
- **Vite** - 下一代前端构建工具，提供极速的开发体验
- **TypeScript** - JavaScript 的超集，提供类型安全
- **TailwindCSS** - 实用优先的 CSS 框架
- **shadcn-vue** - 基于 Radix UI 的高质量组件库
- **Lucide Vue** - 美观的图标库
- **Pinia** - Vue 的状态管理库
- **Vue Router** - Vue.js 的官方路由管理器
- **Axios** - 基于 Promise 的 HTTP 客户端

## 📁 项目结构

```
src/
├── components/          # 公共组件
│   └── ui/             # shadcn-vue 组件
├── views/              # 页面组件
│   ├── Home.vue        # 首页
│   └── About.vue       # 关于页面
├── stores/             # Pinia 状态管理
│   ├── index.ts        # Store 入口
│   └── counter.ts      # 示例 Store
├── router/             # 路由配置
│   └── index.ts        # 路由配置
├── composables/        # 组合式函数
│   └── useApi.ts       # API 相关 composable
├── lib/                # 工具库
│   ├── api.ts          # Axios 配置
│   └── utils.ts        # 通用工具函数
├── types/              # TypeScript 类型定义
│   └── index.ts        # 类型定义
├── assets/             # 静态资源
├── App.vue             # 根组件
├── main.ts             # 应用入口
└── style.css           # 全局样式
```

## 🛠️ 安装和使用

### 安装依赖

```bash
pnpm install
```

### 开发

```bash
pnpm dev
```

### 构建

```bash
pnpm build
```

### 预览构建结果

```bash
pnpm preview
```

## 🎯 主要功能

### 1. 路由管理
- 使用 Vue Router 进行页面路由管理
- 支持懒加载，提升性能
- 导航守卫支持

### 2. 状态管理
- 使用 Pinia 进行状态管理
- 支持 TypeScript 类型推断
- 模块化的 Store 设计

### 3. HTTP 请求
- 使用 Axios 进行 HTTP 请求
- 请求/响应拦截器
- 错误处理机制

### 4. 组合式函数
- 使用 Composition API 编写可复用的逻辑
- 类型安全的 composables

### 5. 样式设计
- TailwindCSS 实用优先的样式
- shadcn-vue 高质量组件
- 响应式设计

## 🔧 配置说明

### Vite 配置
- 路径别名：`@` 指向 `src` 目录
- TailwindCSS 插件集成
- Vue 插件配置

### TailwindCSS 配置
- 使用 TailwindCSS v4
- 支持暗色模式
- 自定义颜色主题

### TypeScript 配置
- 严格类型检查
- 路径映射配置
- Vue 组件类型支持

## 🎨 组件库

项目使用 shadcn-vue 作为组件库，已配置：
- 组件样式：New York 风格
- 图标库：Lucide
- 基础颜色：中性色
- CSS 变量支持

## 📝 开发指南

### 添加新页面
1. 在 `src/views/` 目录下创建新的 Vue 组件
2. 在 `src/router/index.ts` 中添加路由配置
3. 在导航栏中添加对应链接

### 添加新的 Store
1. 在 `src/stores/` 目录下创建新的 store 文件
2. 在 `src/stores/index.ts` 中导出新的 store
3. 在组件中使用 store

### 添加新的 API
1. 在 `src/lib/api.ts` 中添加 API 方法
2. 在 `src/types/index.ts` 中定义相关类型
3. 创建对应的 composable 函数

## 🔗 相关链接

- [Vue 3 文档](https://vuejs.org/)
- [Vite 文档](https://vitejs.dev/)
- [TailwindCSS 文档](https://tailwindcss.com/)
- [shadcn-vue 文档](https://www.shadcn-vue.com/)
- [Pinia 文档](https://pinia.vuejs.org/)
- [Vue Router 文档](https://router.vuejs.org/)

## �� 许可证

MIT License
