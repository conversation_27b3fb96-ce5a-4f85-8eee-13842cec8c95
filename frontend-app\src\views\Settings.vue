<template>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold tracking-tight">系统设置</h1>
        <p class="text-muted-foreground">配置系统参数和应用设置</p>
      </div>
      <Button @click="saveAllSettings" :disabled="saving">
        <Save class="mr-2 h-4 w-4" />
        {{ saving ? '保存中...' : '保存设置' }}
      </Button>
    </div>

    <Tabs default-value="general" class="space-y-4">
      <TabsList class="grid w-full grid-cols-4">
        <TabsTrigger value="general">常规设置</TabsTrigger>
        <TabsTrigger value="oauth">OAuth配置</TabsTrigger>
        <TabsTrigger value="security">安全设置</TabsTrigger>
        <TabsTrigger value="advanced">高级选项</TabsTrigger>
      </TabsList>

      <!-- 常规设置 -->
      <TabsContent value="general" class="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center">
              <Settings2 class="mr-2 h-4 w-4" />
              基本配置
            </CardTitle>
            <CardDescription>系统的基本配置选项</CardDescription>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="grid gap-4 md:grid-cols-2">
              <div class="space-y-2">
                <Label for="appName">应用名称</Label>
                <Input id="appName" v-model="settings.general.appName" placeholder="ChanjetOAuth 管理系统" />
              </div>
              <div class="space-y-2">
                <Label for="appVersion">版本</Label>
                <Input id="appVersion" v-model="settings.general.appVersion" placeholder="v2.1.0" />
              </div>
            </div>
            <div class="space-y-2">
              <Label for="description">系统描述</Label>
              <Input id="description" v-model="settings.general.description" placeholder="OAuth认证管理系统" />
            </div>
            <div class="flex items-center space-x-2">
              <input type="checkbox" id="autoRefresh" v-model="settings.general.autoRefresh" class="rounded" />
              <Label for="autoRefresh">启用自动刷新</Label>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>日志配置</CardTitle>
            <CardDescription>系统日志级别和存储设置</CardDescription>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="space-y-2">
              <Label>日志级别</Label>
              <select v-model="settings.general.logLevel" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm">
                <option value="DEBUG">调试</option>
                <option value="INFO">信息</option>
                <option value="WARNING">警告</option>
                <option value="ERROR">错误</option>
              </select>
            </div>
            <div class="space-y-2">
              <Label for="logRetention">日志保留天数</Label>
              <Input id="logRetention" v-model="settings.general.logRetention" type="number" />
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <!-- OAuth配置 -->
      <TabsContent value="oauth" class="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center">
              <Key class="mr-2 h-4 w-4" />
              OAuth 服务器配置
            </CardTitle>
            <CardDescription>OAuth认证服务器的配置参数</CardDescription>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="grid gap-4 md:grid-cols-2">
              <div class="space-y-2">
                <Label for="authUrl">授权服务器地址</Label>
                <Input id="authUrl" v-model="settings.oauth.authUrl" placeholder="https://auth.example.com" />
              </div>
              <div class="space-y-2">
                <Label for="tokenUrl">Token端点</Label>
                <Input id="tokenUrl" v-model="settings.oauth.tokenUrl" placeholder="/oauth/token" />
              </div>
            </div>
            <div class="grid gap-4 md:grid-cols-2">
              <div class="space-y-2">
                <Label for="clientId">客户端ID</Label>
                <Input id="clientId" v-model="settings.oauth.clientId" placeholder="your-client-id" />
              </div>
              <div class="space-y-2">
                <Label for="clientSecret">客户端密钥</Label>
                <Input id="clientSecret" v-model="settings.oauth.clientSecret" type="password" placeholder="•••••••" />
              </div>
            </div>
            <div class="space-y-2">
              <Label for="scopes">默认权限范围</Label>
              <Input id="scopes" v-model="settings.oauth.scopes" placeholder="read write" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Token 配置</CardTitle>
            <CardDescription>访问令牌的生命周期和刷新设置</CardDescription>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="grid gap-4 md:grid-cols-2">
              <div class="space-y-2">
                <Label for="tokenLifetime">Token 有效期(小时)</Label>
                <Input id="tokenLifetime" v-model="settings.oauth.tokenLifetime" type="number" />
              </div>
              <div class="space-y-2">
                <Label for="refreshThreshold">刷新阈值(小时)</Label>
                <Input id="refreshThreshold" v-model="settings.oauth.refreshThreshold" type="number" />
              </div>
            </div>
            <div class="flex items-center space-x-2">
              <input type="checkbox" id="autoTokenRefresh" v-model="settings.oauth.autoRefresh" class="rounded" />
              <Label for="autoTokenRefresh">启用自动Token刷新</Label>
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <!-- 安全设置 -->
      <TabsContent value="security" class="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center">
              <Shield class="mr-2 h-4 w-4" />
              安全策略
            </CardTitle>
            <CardDescription>系统安全相关配置</CardDescription>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="flex items-center space-x-2">
              <input type="checkbox" id="enableHttps" v-model="settings.security.enableHttps" class="rounded" />
              <Label for="enableHttps">强制使用 HTTPS</Label>
            </div>
            <div class="flex items-center space-x-2">
              <input type="checkbox" id="enableCors" v-model="settings.security.enableCors" class="rounded" />
              <Label for="enableCors">启用 CORS</Label>
            </div>
            <div class="space-y-2">
              <Label for="sessionTimeout">会话超时(分钟)</Label>
              <Input id="sessionTimeout" v-model="settings.security.sessionTimeout" type="number" />
            </div>
            <div class="space-y-2">
              <Label for="maxRetries">最大重试次数</Label>
              <Input id="maxRetries" v-model="settings.security.maxRetries" type="number" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>访问控制</CardTitle>
            <CardDescription>IP白名单和访问限制</CardDescription>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="space-y-2">
              <Label for="allowedIps">允许的IP地址(每行一个)</Label>
              <textarea 
                id="allowedIps" 
                v-model="settings.security.allowedIps" 
                class="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                placeholder="***********&#10;10.0.0.0/8"
              ></textarea>
            </div>
            <div class="flex items-center space-x-2">
              <input type="checkbox" id="enableIpWhitelist" v-model="settings.security.enableIpWhitelist" class="rounded" />
              <Label for="enableIpWhitelist">启用IP白名单</Label>
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <!-- 高级选项 -->
      <TabsContent value="advanced" class="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center">
              <Cog class="mr-2 h-4 w-4" />
              高级配置
            </CardTitle>
            <CardDescription>高级系统配置选项</CardDescription>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="grid gap-4 md:grid-cols-2">
              <div class="space-y-2">
                <Label for="dbHost">数据库主机</Label>
                <Input id="dbHost" v-model="settings.advanced.dbHost" placeholder="localhost" />
              </div>
              <div class="space-y-2">
                <Label for="dbPort">数据库端口</Label>
                <Input id="dbPort" v-model="settings.advanced.dbPort" type="number" placeholder="5432" />
              </div>
            </div>
            <div class="grid gap-4 md:grid-cols-2">
              <div class="space-y-2">
                <Label for="redisHost">Redis主机</Label>
                <Input id="redisHost" v-model="settings.advanced.redisHost" placeholder="localhost" />
              </div>
              <div class="space-y-2">
                <Label for="redisPort">Redis端口</Label>
                <Input id="redisPort" v-model="settings.advanced.redisPort" type="number" placeholder="6379" />
              </div>
            </div>
            <div class="space-y-2">
              <Label for="apiRateLimit">API限流(请求/分钟)</Label>
              <Input id="apiRateLimit" v-model="settings.advanced.apiRateLimit" type="number" placeholder="1000" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle class="text-red-600">危险操作</CardTitle>
            <CardDescription>请谨慎执行以下操作</CardDescription>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="space-y-2">
              <Button variant="outline" class="w-full" @click="resetToDefault">
                <RotateCcw class="mr-2 h-4 w-4" />
                重置为默认设置
              </Button>
              <Button variant="destructive" class="w-full" @click="clearAllData">
                <Trash2 class="mr-2 h-4 w-4" />
                清空所有数据
              </Button>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { 
  Save, 
  Settings2, 
  Key, 
  Shield, 
  Cog, 
  RotateCcw, 
  Trash2 
} from 'lucide-vue-next'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

const saving = ref(false)

const settings = ref({
  general: {
    appName: 'ChanjetOAuth 管理系统',
    appVersion: 'v2.1.0',
    description: 'OAuth认证管理系统',
    autoRefresh: true,
    logLevel: 'INFO',
    logRetention: 30
  },
  oauth: {
    authUrl: 'https://auth.example.com',
    tokenUrl: '/oauth/token',
    clientId: 'your-client-id',
    clientSecret: '',
    scopes: 'read write',
    tokenLifetime: 6,
    refreshThreshold: 24,
    autoRefresh: true
  },
  security: {
    enableHttps: true,
    enableCors: true,
    sessionTimeout: 30,
    maxRetries: 3,
    allowedIps: '***********\n10.0.0.0/8',
    enableIpWhitelist: false
  },
  advanced: {
    dbHost: 'localhost',
    dbPort: 5432,
    redisHost: 'localhost',
    redisPort: 6379,
    apiRateLimit: 1000
  }
})

const saveAllSettings = async () => {
  saving.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    console.log('保存设置:', settings.value)
  } finally {
    saving.value = false
  }
}

const resetToDefault = () => {
  if (confirm('确定要重置为默认设置吗？此操作无法撤销')) {
    console.log('重置为默认设置')
  }
}

const clearAllData = () => {
  if (confirm('确定要清空所有数据吗？此操作无法撤销')) {
    console.log('清空所有数据')
  }
}
</script>
