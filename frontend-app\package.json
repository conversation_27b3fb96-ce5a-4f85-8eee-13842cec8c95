{"name": "frontend-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:skip-check": "vite --mode development", "build": "vue-tsc -b && vite build", "build:skip-check": "vite build", "preview": "vite preview", "type-check": "vue-tsc --noEmit -p tsconfig.app.json --composite false", "lint": "vue-tsc --noEmit", "clean": "rm -rf dist node_modules/.vite"}, "dependencies": {"@tailwindcss/vite": "^4.1.10", "@tanstack/vue-table": "^8.21.3", "@vee-validate/zod": "^4.15.1", "@vueuse/core": "^13.3.0", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-vue": "^8.6.0", "lucide-vue-next": "^0.514.0", "pinia": "^3.0.3", "reka-ui": "^2.3.1", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.10", "tw-animate-css": "^1.3.4", "vaul-vue": "^0.4.1", "vee-validate": "^4.15.1", "vue": "^3.5.13", "vue-router": "^4.5.1", "vue-sonner": "^2.0.0", "zod": "^3.25.64"}, "devDependencies": {"@types/node": "^24.0.1", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "vite": "^6.3.5", "vue-tsc": "^2.2.8"}}