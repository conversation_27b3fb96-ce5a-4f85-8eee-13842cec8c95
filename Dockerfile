FROM mcr.microsoft.com/dotnet/aspnet:5.0 AS base
WORKDIR /app
EXPOSE 35010

FROM mcr.microsoft.com/dotnet/sdk:5.0 AS build
WORKDIR /src
COPY ["ChanjetOAuthTest.csproj", "."]
RUN dotnet restore "ChanjetOAuthTest.csproj"
COPY . .
WORKDIR "/src"
RUN dotnet build "ChanjetOAuthTest.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "ChanjetOAuthTest.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "ChanjetOAuthTest.dll"] 