﻿using ChanjetOAuthTest.App.Models;
using ChanjetOAuthTest.App.Services;
using ChanjetOAuthTest.App.Configuration;
using ChanjetOAuthTest.App.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using System.Security.Cryptography;

namespace ChanjetOAuthTest.App.Controllers
{
    [ApiController]
    [Route("[controller]")]
    public class OAuthController : ControllerBase
    {
        private readonly ChanjetConfig _chanjetConfig;
        private readonly ITokenManager _tokenManager;
        private readonly ILogger<OAuthController> _logger;

        public OAuthController(
            IOptions<ChanjetConfig> chanjetConfig, 
            ITokenManager tokenManager,
            ILogger<OAuthController> logger)
        {
            _chanjetConfig = chanjetConfig.Value;
            _tokenManager = tokenManager;
            _logger = logger;
        }

        /// <summary>
        /// 第一步：重定向到畅捷通授权页面
        /// 用户访问此接口后会跳转到畅捷通授权页面，授权完成后自动回调到 /oauth/callback
        /// </summary>
        [HttpGet("authorize")]
        public IActionResult Authorize([FromQuery] string userId = null)
        {
            try
            {
                _logger.LogInformation("开始OAuth授权流程，用户ID: {UserId}", userId ?? "default");

                // 构造畅捷通授权URL
                var authUrl = $"{_chanjetConfig.MarketAuthorizeUrl}?" +
                         $"appKey={_chanjetConfig.AppKey}&" +
                         $"appName={_chanjetConfig.AppName}&" +
                         $"scope={_chanjetConfig.Scope}";

                _logger.LogInformation("重定向到授权URL: {AuthUrl}", authUrl);

                return Redirect(authUrl);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "构造授权URL时发生错误");
                return StatusCode(500, new
                {
                    success = false,
                    message = "构造授权URL失败",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// 第二步：接收畅捷通的回调请求 - 自动完成永久授权流程
        /// </summary>
        [HttpGet("callback")]
        public async Task<IActionResult> Callback([FromQuery] string code, [FromQuery] string state = null)
        {
            if (string.IsNullOrEmpty(code))
            {
                _logger.LogWarning("授权回调失败，缺少code参数");
                return BadRequest(new
                {
                    success = false,
                    message = "授权回调失败，缺少code参数",
                    suggestion = "请重新进行授权"
                });
            }

            var userId = state ?? "default";

            try
            {
                _logger.LogInformation("收到授权回调，授权码: {Code}, 用户ID: {UserId}", code, userId);

                // 使用TokenManager获取Token
                var tokenInfo = await _tokenManager.GetTokenByAuthCodeAsync(code, userId);

                if (tokenInfo == null)
                {
                    _logger.LogError("Token获取失败，返回null");
                    return StatusCode(500, new
                    {
                        success = false,
                        message = "Token获取失败",
                        suggestion = "请检查授权码是否有效，或稍后重试"
                    });
                }

                _logger.LogInformation("Token获取成功，用户ID: {UserId}, 过期时间: {ExpiresAt}", 
                    userId, tokenInfo.ExpiresAt);

                // 返回成功页面，显示Token信息（生产环境中不应该直接显示敏感信息）
                var html = GenerateSuccessPage(tokenInfo);
                return Content(html, "text/html");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理授权回调时发生错误，用户ID: {UserId}", userId);
                return StatusCode(500, new
                {
                    success = false,
                    message = "处理授权回调时发生错误",
                    error = ex.Message,
                    suggestion = "请检查网络连接和配置，或稍后重试授权流程"
                });
            }
        }

        /// <summary>
        /// 获取授权页面 - 方便用户直接访问进行授权
        /// </summary>
        [HttpGet("page")]
        public IActionResult AuthorizePage()
        {
            var html = @"
<!DOCTYPE html>
<html>
<head>
    <title>畅捷通OAuth授权</title>
    <meta charset='utf-8'>
    <style>
        body { font-family: Arial, sans-serif; margin: 50px; text-align: center; }
        .container { max-width: 600px; margin: 0 auto; }
        .button { background: #007bff; color: white; padding: 15px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; text-decoration: none; display: inline-block; margin: 10px; }
        .button:hover { background: #0056b3; }
        .info { background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>畅捷通OAuth授权</h1>
        <div class='info'>
            <p>点击下面的按钮开始OAuth授权流程</p>
            <p>授权完成后，系统将自动获取Token并开始监控</p>
        </div>
        <a href='/oauth/authorize' class='button'>开始授权</a>
        <br>
        <a href='/api/tokentest/dashboard' class='button'>查看Token管理面板</a>
    </div>
</body>
</html>";
            return Content(html, "text/html");
        }

        /// <summary>
        /// 生成授权成功页面
        /// </summary>
        private string GenerateSuccessPage(TokenInfo tokenInfo)
        {
            return $@"
<!DOCTYPE html>
<html>
<head>
    <title>授权成功</title>
    <meta charset='utf-8'>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 50px; }}
        .container {{ max-width: 800px; margin: 0 auto; }}
        .success {{ background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0; }}
        .info {{ background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin: 20px 0; }}
        .button {{ background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 3px; cursor: pointer; margin: 5px; text-decoration: none; display: inline-block; }}
        .button:hover {{ background: #0056b3; }}
        table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
        th, td {{ border: 1px solid #ddd; padding: 12px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
        .token {{ font-family: monospace; word-break: break-all; }}
    </style>
</head>
<body>
    <div class='container'>
        <h1>OAuth授权成功！</h1>
        
        <div class='success'>
            <h3>✅ 授权完成</h3>
            <p>您的Token已成功获取并开始自动监控。系统将在Token即将过期时自动刷新。</p>
        </div>

        <div class='info'>
            <h3>Token信息</h3>
            <table>
                <tr><th>用户ID</th><td>{tokenInfo.UserId}</td></tr>
                <tr><th>Token类型</th><td>{tokenInfo.TokenType}</td></tr>
                <tr><th>作用域</th><td>{tokenInfo.Scope}</td></tr>
                <tr><th>过期时间</th><td>{tokenInfo.ExpiresAt:yyyy-MM-dd HH:mm:ss}</td></tr>
                <tr><th>刷新Token过期时间</th><td>{tokenInfo.RefreshExpiresAt:yyyy-MM-dd HH:mm:ss}</td></tr>
                <tr><th>创建时间</th><td>{tokenInfo.CreatedAt:yyyy-MM-dd HH:mm:ss}</td></tr>
                <tr><th>状态</th><td>✅ 有效</td></tr>
            </table>
        </div>

        <div class='info'>
            <h3>下一步操作</h3>
            <p>现在您可以：</p>
            <ul>
                <li>使用Token调用畅捷通API</li>
                <li>查看Token管理面板监控Token状态</li>
                <li>系统会自动处理Token刷新，无需手动操作</li>
            </ul>
        </div>

        <div style='text-align: center; margin: 30px 0;'>
            <a href='/api/tokentest/dashboard' class='button'>查看Token管理面板</a>
            <a href='/api/token/status?userId={tokenInfo.UserId}' class='button'>检查Token状态</a>
            <a href='/oauth/page' class='button'>重新授权</a>
        </div>
    </div>
</body>
</html>";
        }
    }
}
