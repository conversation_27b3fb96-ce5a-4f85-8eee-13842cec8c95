using System;
using System.Text.Json.Serialization;

namespace ChanjetOAuthTest.App.Models
{
    public class TokenInfo
    {
        [JsonPropertyName("access_token")]
        public string AccessToken { get; set; }

        [JsonPropertyName("refresh_token")]
        public string RefreshToken { get; set; }

        [JsonPropertyName("expires_in")]
        public int ExpiresIn { get; set; }

        [JsonPropertyName("refresh_expires_in")]
        public int RefreshExpiresIn { get; set; }

        [JsonPropertyName("token_type")]
        public string TokenType { get; set; }

        [JsonPropertyName("scope")]
        public string Scope { get; set; }

        /// <summary>
        /// AccessToken的过期时间
        /// </summary>
        [JsonPropertyName("expires_at")]
        public DateTime ExpiresAt { get; set; }

        /// <summary>
        /// RefreshToken的过期时间
        /// </summary>
        [JsonPropertyName("refresh_expires_at")]
        public DateTime RefreshExpiresAt { get; set; }

        /// <summary>
        /// Token创建时间
        /// </summary>
        [JsonPropertyName("created_at")]
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 用户ID或标识符
        /// </summary>
        [JsonPropertyName("user_id")]
        public string UserId { get; set; }

        /// <summary>
        /// 用户永久授权码（用于长期获取token，无需用户重复授权）
        /// </summary>
        [JsonPropertyName("user_auth_permanent_code")]
        public string UserAuthPermanentCode { get; set; }

        /// <summary>
        /// 检查AccessToken是否即将过期（提前5分钟刷新）
        /// </summary>
        [JsonIgnore]
        public bool IsAccessTokenExpiring => DateTime.UtcNow.AddMinutes(5) >= ExpiresAt;

        /// <summary>
        /// 检查RefreshToken是否过期
        /// </summary>
        [JsonIgnore]
        public bool IsRefreshTokenExpired => DateTime.UtcNow >= RefreshExpiresAt;

        /// <summary>
        /// 检查AccessToken是否已过期
        /// </summary>
        [JsonIgnore]
        public bool IsAccessTokenExpired => DateTime.UtcNow >= ExpiresAt;

        /// <summary>
        /// 计算过期时间
        /// </summary>
        public void CalculateExpirationTimes()
        {
            var now = DateTime.UtcNow;
            ExpiresAt = now.AddSeconds(ExpiresIn);
            RefreshExpiresAt = now.AddSeconds(RefreshExpiresIn);
            CreatedAt = now;
        }
    }
} 