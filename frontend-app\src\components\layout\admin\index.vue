<script lang="ts">
export const description = 'An inset sidebar with secondary navigation.'
export const iframeHeight = '800px'
</script>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import AppSidebar from '@/components/layout/admin/components/AppSidebar.vue'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb'
import { Separator } from '@/components/ui/separator'
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from '@/components/ui/sidebar'

const route = useRoute()

// 根据路由生成面包屑
const breadcrumbItems = computed(() => {
  const routeMap = {
    '/': { title: '管理面板', parent: null },
    '/oauth': { title: 'OAuth授权', parent: '/' },
    '/tokens': { title: 'Token管理', parent: '/' },
    '/data-sync': { title: '数据同步', parent: '/' },
    '/monitoring': { title: '系统监控', parent: '/' },
    '/settings': { title: '系统设置', parent: '/' }
  }
  
  const currentRoute = routeMap[route.path as keyof typeof routeMap]
  if (!currentRoute) return []
  
  const items = []
  if (currentRoute.parent && route.path !== '/') {
    items.push({ title: '管理面板', href: '/' })
  }
  if (route.path !== '/') {
    items.push({ title: currentRoute.title, href: route.path })
  }
  
  return items
})

// 当前页面标题
const currentPageTitle = computed(() => {
  const routeMap = {
    '/': '管理面板',
    '/oauth': 'OAuth授权',
    '/tokens': 'Token管理',
    '/data-sync': '数据同步',
    '/monitoring': '系统监控',
    '/settings': '系统设置'
  }
  return routeMap[route.path as keyof typeof routeMap] || '管理面板'
})
</script>

<template>
  <SidebarProvider>
    <AppSidebar />
    <SidebarInset>
      <!-- 顶部导航栏 - 调整样式和布局 -->
      <header class="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
        <div class="flex items-center gap-2 px-4">
          <!-- 调整收起按钮大小 -->
          <SidebarTrigger class="-ml-1 h-7 w-7" />
          <Separator orientation="vertical" class="mr-2 h-4" />
          <!-- 动态面包屑 -->
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem v-if="breadcrumbItems.length > 1" class="hidden md:block">
                <BreadcrumbLink :href="breadcrumbItems[0]?.href">
                  {{ breadcrumbItems[0]?.title }}
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator v-if="breadcrumbItems.length > 1" class="hidden md:block" />
              <BreadcrumbItem>
                <BreadcrumbPage>{{ currentPageTitle }}</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>
      </header>
      
      <!-- 主内容区域 - 使用插槽渲染用户程序 -->
      <div class="flex flex-1 flex-col gap-4 p-4 pt-0">
        <slot />
      </div>
    </SidebarInset>
  </SidebarProvider>
</template>
