<template>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold tracking-tight">系统监控</h1>
        <p class="text-muted-foreground">实时监控系统性能和运行状态</p>
      </div>
      <Button @click="refreshMetrics" :disabled="loading">
        <RefreshCw class="mr-2 h-4 w-4" :class="{ 'animate-spin': loading }" />
        刷新数据
      </Button>
    </div>

    <!-- 系统指标 -->
    <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">CPU 使用率</CardTitle>
          <Cpu class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ metrics.cpu }}%</div>
          <Progress :value="metrics.cpu" class="mt-2" />
        </CardContent>
      </Card>

      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">内存使用</CardTitle>
          <MemoryStick class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ metrics.memory }}%</div>
          <Progress :value="metrics.memory" class="mt-2" />
        </CardContent>
      </Card>

      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">磁盘使用</CardTitle>
          <HardDrive class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ metrics.disk }}%</div>
          <Progress :value="metrics.disk" class="mt-2" />
        </CardContent>
      </Card>

      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">网络流量</CardTitle>
          <Network class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ metrics.network }}</div>
          <p class="text-xs text-muted-foreground">MB/s</p>
        </CardContent>
      </Card>
    </div>

    <!-- 服务状态-->
    <div class="grid gap-6 lg:grid-cols-2">
      <Card>
        <CardHeader>
          <CardTitle class="flex items-center">
            <Server class="mr-2 h-4 w-4 text-blue-500" />
            服务状态
          </CardTitle>
          <CardDescription>各项服务的运行状态</CardDescription>
        </CardHeader>
        <CardContent>
          <div class="space-y-4">
            <div v-for="service in services" :key="service.name" class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div :class="service.status === 'running' ? 'bg-green-500' : 'bg-red-500'" class="w-3 h-3 rounded-full"></div>
                <div>
                  <h4 class="font-medium">{{ service.name }}</h4>
                  <p class="text-sm text-muted-foreground">{{ service.description }}</p>
                </div>
              </div>
              <Badge :variant="service.status === 'running' ? 'default' : 'destructive'">
                {{ service.status === 'running' ? '运行中' : '已停止' }}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle class="flex items-center">
            <AlertTriangle class="mr-2 h-4 w-4 text-orange-500" />
            系统警告
          </CardTitle>
          <CardDescription>最近的系统警告和错误</CardDescription>
        </CardHeader>
        <CardContent>
          <div class="space-y-3">
            <div v-for="alert in alerts" :key="alert.id" class="flex items-start space-x-3 p-3 border rounded-lg">
              <component :is="getAlertIcon(alert.level)" :class="getAlertColor(alert.level)" class="h-4 w-4 mt-0.5" />
              <div class="flex-1">
                <h4 class="font-medium text-sm">{{ alert.message }}</h4>
                <p class="text-xs text-muted-foreground">{{ alert.time }}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- 性能图表区域 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center">
          <Activity class="mr-2 h-4 w-4 text-green-500" />
          性能趋势
        </CardTitle>
        <CardDescription>系统性能数据实时监控图表</CardDescription>
      </CardHeader>
      <CardContent>
        <div class="h-64 flex items-center justify-center border-2 border-dashed border-gray-300 rounded-lg">
          <div class="text-center">
            <TrendingUp class="mx-auto h-12 w-12 text-gray-400" />
            <h3 class="mt-4 text-lg font-medium text-gray-900">性能图表</h3>
            <p class="text-gray-500">实时性能监控图表将在此显示</p>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { 
  RefreshCw, 
  Cpu, 
  MemoryStick, 
  HardDrive, 
  Network, 
  Server, 
  AlertTriangle, 
  Activity,
  TrendingUp,
  Info,
  AlertCircle,
  XCircle
} from 'lucide-vue-next'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'

const loading = ref(false)
let refreshInterval: any = null

const metrics = ref({
  cpu: 45,
  memory: 68,
  disk: 72,
  network: 2.3
})

const services = ref([
  { name: 'OAuth服务', description: 'OAuth认证服务', status: 'running' },
  { name: 'Token服务', description: 'Token管理服务', status: 'running' },
  { name: '数据同步', description: 'PostgreSQL数据同步', status: 'running' },
  { name: '缓存服务', description: 'Redis缓存', status: 'running' },
  { name: '同步服务', description: '数据同步服务', status: 'stopped' }
])

const alerts = ref([
  {
    id: 1,
    level: 'warning',
    message: '内存使用率超70%',
    time: '5分钟前'
  },
  {
    id: 2,
    level: 'info',
    message: '数据同步服务已重启',
    time: '15分钟前'
  },
  {
    id: 3,
    level: 'error',
    message: '同步服务连接失败',
    time: '1小时前'
  }
])

const getAlertIcon = (level: string) => {
  switch (level) {
    case 'error': return XCircle
    case 'warning': return AlertTriangle
    case 'info': return Info
    default: return AlertCircle
  }
}

const getAlertColor = (level: string) => {
  switch (level) {
    case 'error': return 'text-red-500'
    case 'warning': return 'text-orange-500'
    case 'info': return 'text-blue-500'
    default: return 'text-gray-500'
  }
}

const refreshMetrics = async () => {
  loading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    // 模拟数据更新
    metrics.value = {
      cpu: Math.floor(Math.random() * 100),
      memory: Math.floor(Math.random() * 100),
      disk: Math.floor(Math.random() * 100),
      network: +(Math.random() * 10).toFixed(1)
    }
  } finally {
    loading.value = false
  }
}

const startAutoRefresh = () => {
  refreshInterval = setInterval(() => {
    refreshMetrics()
  }, 30000) // 30秒刷新一次
}

onMounted(() => {
  refreshMetrics()
  startAutoRefresh()
})

onUnmounted(() => {
  if (refreshInterval) {
    clearInterval(refreshInterval)
  }
})
</script>
