using System.Collections.Generic;

namespace ChanjetOAuthTest.App.Configuration
{
    /// <summary>
    /// 外部Token服务配置类
    /// </summary>
    public class ExternalTokenConfig
    {
        /// <summary>
        /// 客户端凭据列表
        /// </summary>
        public List<ClientCredential> ClientCredentials { get; set; } = new List<ClientCredential>();
    }

    /// <summary>
    /// 客户端凭据配置
    /// </summary>
    public class ClientCredential
    {
        /// <summary>
        /// 客户端ID
        /// </summary>
        public string ClientId { get; set; }

        /// <summary>
        /// 客户端名称
        /// </summary>
        public string ClientName { get; set; }

        /// <summary>
        /// 客户端密钥
        /// </summary>
        public string ClientSecret { get; set; }
    }
} 