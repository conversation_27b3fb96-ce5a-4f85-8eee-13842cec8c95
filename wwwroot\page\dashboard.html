<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Token管理 - ChanjetOAuth</title>
    <script src="/src/js/tailwindcss.js"></script>
    <script src="/src/js/lucide.min.js"></script>
    <script src="/src/js/vue.global.js"></script>
    <style>
        /* 自定义全局样式和字体 */
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f7fafc;
        }

        /* 美化滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        /* 导航栏样式 */
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        /* 加载动画 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.6s ease-out forwards;
        }
    </style>
</head>

<body class="text-gray-800 bg-gray-100">
    <div id="app">
        <button @click="toggleSidebar"
            class="lg:hidden fixed top-4 left-4 z-50 bg-blue-600 text-white p-3 rounded-lg shadow-lg hover:bg-blue-700 transition-colors">
            <i data-lucide="menu" class="w-6 h-6"></i>
        </button>

        <nav class="sidebar fixed left-0 top-0 h-full w-64 z-40 transform transition-transform duration-300 lg:translate-x-0"
            :class="{ '-translate-x-full': !sidebarOpen }">
            <div class="p-6 border-b border-white/20">
                <div class="flex items-center">
                    <div class="bg-white/20 p-2 rounded-lg mr-3">
                        <i data-lucide="shield-check" class="h-6 w-6 text-white"></i>
                    </div>
                    <div>
                        <h2 class="text-xl font-bold text-white">管理面板</h2>
                        <p class="text-sm text-white/70">ChanjetOAuth</p>
                    </div>
                </div>
            </div>

            <div class="p-4">
                <nav class="space-y-2">
                    <a href="/page/index.html"
                        class="nav-link flex items-center px-4 py-3 text-white/80 hover:text-white hover:bg-white/20 rounded-lg transition-all">
                        <i data-lucide="home" class="w-5 h-5 mr-3"></i>
                        首页
                    </a>
                    <a href="/oauth/page"
                        class="nav-link flex items-center px-4 py-3 text-white/80 hover:text-white hover:bg-white/20 rounded-lg transition-all">
                        <i data-lucide="key" class="w-5 h-5 mr-3"></i>
                        OAuth授权
                    </a>
                    <a href="/page/dashboard.html"
                        class="nav-link flex items-center px-4 py-3 text-white bg-white/20 rounded-lg">
                        <i data-lucide="activity" class="w-5 h-5 mr-3"></i>
                        Token管理
                    </a>
                    <a href="/page/data-sync.html"
                        class="nav-link flex items-center px-4 py-3 text-white/80 hover:text-white hover:bg-white/20 rounded-lg transition-all">
                        <i data-lucide="refresh-cw" class="w-5 h-5 mr-3"></i>
                        数据同步
                    </a>
                    <a href="#" @click.prevent="showFeatureComingSoon('系统监控')"
                        class="nav-link flex items-center px-4 py-3 text-white/80 hover:text-white hover:bg-white/20 rounded-lg transition-all">
                        <i data-lucide="monitor" class="w-5 h-5 mr-3"></i>
                        系统监控
                    </a>
                    <a href="#" @click.prevent="showFeatureComingSoon('系统设置')"
                        class="nav-link flex items-center px-4 py-3 text-white/80 hover:text-white hover:bg-white/20 rounded-lg transition-all">
                        <i data-lucide="settings" class="w-5 h-5 mr-3"></i>
                        系统设置
                    </a>
                </nav>
            </div>
        </nav>

        <main class="lg:ml-64 min-h-screen">
            <div class="p-6">
                <div class="mb-8">
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">Token管理</h1>
                    <p class="text-gray-600">管理和监控OAuth访问令牌的状态和生命周期</p>
                </div>

                <div class="grid grid-cols-1 xl:grid-cols-3 gap-6 mb-8">
                    <div class="xl:col-span-2 space-y-6">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                                <div class="flex items-center mb-4">
                                    <div class="bg-green-100 p-3 rounded-lg mr-4">
                                        <i data-lucide="key-round" class="h-6 w-6 text-green-600"></i>
                                    </div>
                                    <div>
                                        <h3 class="text-lg font-semibold text-gray-900">获取Token</h3>
                                        <p class="text-sm text-gray-500">输入授权码获取6天Token</p>
                                    </div>
                                </div>

                                <div class="space-y-3">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">授权码 *</label>
                                        <input v-model="authCode" type="text" placeholder="10分钟内有效，仅能使用一次"
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm">
                                    </div>

                                    <button @click="getTokenByAuthCode" :disabled="!authCode.trim() || loading"
                                        class="w-full flex items-center justify-center px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                                        <i data-lucide="download" class="w-4 h-4 mr-2"></i>
                                        获取Token
                                    </button>

                                    <p class="text-xs text-gray-500">
                                        获取后Token有效期6天，系统会自动在过期前刷新
                                    </p>
                                </div>
                            </div>

                            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                                <div class="flex items-center mb-4">
                                    <div class="bg-blue-100 p-3 rounded-lg mr-4">
                                        <i data-lucide="user" class="h-6 w-6 text-blue-600"></i>
                                    </div>
                                    <div>
                                        <h3 class="text-lg font-semibold text-gray-900">用户选择</h3>
                                        <p class="text-sm text-gray-500">选择要管理的用户</p>
                                    </div>
                                </div>

                                <div class="space-y-3">
                                    <select v-model="selectedUserId" @change="loadTokenStatus"
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                        <option v-if="clientCredentials.length === 0" value="default">加载中...</option>
                                        <option v-for="client in clientCredentials" :key="client.clientId" :value="client.clientId">
                                            {{ client.clientName }}
                                        </option>
                                    </select>

                                    <button @click="loadTokenStatus" :disabled="loading"
                                        class="w-full flex items-center justify-center px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                                        <i data-lucide="refresh-cw" class="w-4 h-4 mr-2"
                                            :class="{ 'animate-spin': loading }"></i>
                                        {{ loading ? '加载中...' : '刷新状态' }}
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                            <div class="flex items-center justify-between mb-6">
                                <div class="flex items-center">
                                    <div class="bg-gray-100 p-3 rounded-lg mr-4">
                                        <i data-lucide="info" class="h-6 w-6 text-gray-600"></i>
                                    </div>
                                    <div>
                                        <h3 class="text-lg font-semibold text-gray-900">Token详细信息</h3>
                                        <p class="text-sm text-gray-500">完整的Token数据和元信息</p>
                                    </div>
                                </div>
                                <div class="flex space-x-2">
                                    <button @click="copyTokenToClipboard" :disabled="!tokenStatus.accessToken"
                                        class="flex items-center px-3 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                                        <i data-lucide="copy" class="w-4 h-4 mr-2"></i>
                                        复制Token
                                    </button>
                                    <button @click="refreshToken" :disabled="!tokenStatus.refreshToken || loading"
                                        class="flex items-center px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                                        <i data-lucide="rotate-cw" class="w-4 h-4 mr-2"
                                            :class="{ 'animate-spin': loading }"></i>
                                        刷新Token
                                    </button>
                                </div>
                            </div>

                            <div class="space-y-4">
                                <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">访问令牌 (Access
                                            Token)</label>
                                        <div class="relative">
                                            <textarea :value="tokenData.access_token || '暂无数据'" readonly
                                                class="w-full h-32 px-4 py-3 text-sm bg-gray-50 border border-gray-300 rounded-lg font-mono resize-none"></textarea>
                                            <div class="absolute top-2 right-2">
                                                <span
                                                    :class="tokenStatus.accessToken ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                                                    class="px-2 py-1 text-xs font-medium rounded-full">
                                                    {{ tokenStatus.accessToken ? '有效' : '无效' }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">刷新令牌 (Refresh
                                            Token)</label>
                                        <div class="relative">
                                            <textarea :value="tokenData.refresh_token || '暂无数据'" readonly
                                                class="w-full h-32 px-4 py-3 text-sm bg-gray-50 border border-gray-300 rounded-lg font-mono resize-none"></textarea>
                                            <div class="absolute top-2 right-2">
                                                <span
                                                    :class="tokenStatus.refreshToken ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                                                    class="px-2 py-1 text-xs font-medium rounded-full">
                                                    {{ tokenStatus.refreshToken ? '有效' : '无效' }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                                    <div class="flex items-center mb-3">
                                        <div class="bg-yellow-100 p-2 rounded-lg mr-3">
                                            <i data-lucide="star" class="h-5 w-5 text-yellow-600"></i>
                                        </div>
                                        <div>
                                            <h4 class="text-sm font-semibold text-yellow-800">永久授权码 (重要 暂时无法使用)</h4>
                                            <p class="text-xs text-yellow-700">可用于后续无需用户重复授权直接获取新Token</p>
                                        </div>
                                        <button @click="copyPermanentCode"
                                            class="ml-auto flex items-center px-3 py-1 bg-yellow-600 text-white text-sm rounded-lg hover:bg-yellow-700 transition-colors">
                                            <i data-lucide="copy" class="w-3 h-3 mr-1"></i>
                                            复制
                                        </button>
                                    </div>
                                    <div class="bg-yellow-100 rounded p-3 font-mono text-sm break-all">
                                        {{ tokenData.user_auth_permanent_code || '暂无数据' }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="space-y-6">
                        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                            <div class="flex items-center mb-6">
                                <div class="bg-green-100 p-3 rounded-lg mr-4">
                                    <i data-lucide="shield" class="h-6 w-6 text-green-600"></i>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900">Token状态概览</h3>
                                    <p class="text-sm text-gray-500">当前选择用户的Token详细信息</p>
                                </div>
                            </div>

                            <div class="space-y-4">
                                <div class="bg-blue-50 rounded-lg p-4">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <p class="text-sm text-blue-600 font-medium">访问令牌</p>
                                            <p class="text-xs text-blue-500 mt-1">Access Token</p>
                                        </div>
                                        <div :class="tokenStatus.accessToken ? 'bg-green-500' : 'bg-gray-400'"
                                            class="w-3 h-3 rounded-full"></div>
                                    </div>
                                    <p class="text-lg font-bold text-blue-900 mt-2">
                                        {{ tokenStatus.accessToken ? '有效' : '无效' }}
                                    </p>
                                </div>

                                <div class="bg-purple-50 rounded-lg p-4">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <p class="text-sm text-purple-600 font-medium">刷新令牌</p>
                                            <p class="text-xs text-purple-500 mt-1">Refresh Token</p>
                                        </div>
                                        <div :class="tokenStatus.refreshToken ? 'bg-green-500' : 'bg-gray-400'"
                                            class="w-3 h-3 rounded-full"></div>
                                    </div>
                                    <p class="text-lg font-bold text-purple-900 mt-2">
                                        {{ tokenStatus.refreshToken ? '有效' : '无效' }}
                                    </p>
                                </div>

                                <div class="bg-orange-50 rounded-lg p-4">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <p class="text-sm text-orange-600 font-medium">过期时间</p>
                                            <p class="text-xs text-orange-500 mt-1">Expires In</p>
                                        </div>
                                        <div :class="tokenStatus.isExpired ? 'bg-red-500' : 'bg-green-500'"
                                            class="w-3 h-3 rounded-full"></div>
                                    </div>
                                    <p class="text-lg font-bold text-orange-900 mt-2">
                                        {{ tokenStatus.expiresIn || '未知' }}
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                            <div class="flex items-center mb-6">
                                <div class="bg-yellow-100 p-3 rounded-lg mr-4">
                                    <i data-lucide="zap" class="h-6 w-6 text-yellow-600"></i>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900">Token操作</h3>
                                    <p class="text-sm text-gray-500">执行Token相关的管理操作</p>
                                </div>
                            </div>

                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <button @click="validateToken" :disabled="!tokenStatus.accessToken || loading"
                                    class="flex items-center justify-center px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                                    <i data-lucide="check-circle" class="w-4 h-4 mr-2"></i>
                                    验证Token
                                </button>
                                <button @click="revokeToken" :disabled="!tokenStatus.accessToken || loading"
                                    class="flex items-center justify-center px-4 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                                    <i data-lucide="x-circle" class="w-4 h-4 mr-2"></i>
                                    撤销Token
                                </button>
                                <button @click="exportToken" :disabled="!tokenStatus.accessToken"
                                    class="flex items-center justify-center px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                                    <i data-lucide="download" class="w-4 h-4 mr-2"></i>
                                    导出Token
                                </button>
                                <button @click="clearToken"
                                    class="flex items-center justify-center px-4 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                                    <i data-lucide="trash-2" class="w-4 h-4 mr-2"></i>
                                    清空缓存
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl shadow-sm border border-blue-200 p-6">
                    <div class="flex items-center mb-4">
                        <div class="bg-blue-100 p-3 rounded-lg mr-4">
                            <i data-lucide="book-open" class="h-6 w-6 text-blue-600"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-blue-900">授权码获取说明</h3>
                            <p class="text-sm text-blue-600">如何获取授权码来获取Token</p>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg p-4 mb-4">
                        <h4 class="font-semibold text-gray-900 mb-2">1. 访问授权地址（替换YOUR_APP_KEY等参数）:</h4>
                        <div class="bg-gray-100 rounded p-3 font-mono text-sm break-all relative group">
                            <span
                                class="text-gray-800">https://market.chanjet.com/user/v2/authorize?appkey=YOUR_APP_KEY&redirect_uri=YOUR_REDIRECT_URI&scope=auth_all&state=test123</span>
                            <button @click="copyAuthUrl"
                                class="absolute top-2 right-2 bg-gray-600 text-white p-1 rounded opacity-0 group-hover:opacity-100 transition-opacity">
                                <i data-lucide="copy" class="h-3 w-3"></i>
                            </button>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-2">操作步骤:</h4>
                            <ol class="list-decimal list-inside space-y-1 text-gray-700">
                                <li>用户访问授权地址并登录</li>
                                <li>用户同意授权</li>
                                <li>系统跳转到回调地址，URL中包含code参数</li>
                                <li>复制code值到上面的输入框</li>
                                <li>点击"获取Token"按钮</li>
                            </ol>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-2">重要提示:</h4>
                            <ul class="list-disc list-inside space-y-1 text-gray-700">
                                <li>授权码有效期<strong>10分钟</strong></li>
                                <li>每个授权码<strong>仅能使用一次</strong></li>
                                <li>Token有效期<strong>6天</strong></li>
                                <li>务必保存永久授权码以备后续使用</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <div v-if="notification.show"
            class="fixed top-6 right-6 max-w-sm bg-white border border-gray-200 rounded-xl shadow-lg p-4 z-50 transition-all duration-300"
            :class="notification.type === 'success' ? 'border-green-200' : notification.type === 'error' ? 'border-red-200' : 'border-blue-200'">
            <div class="flex items-start">
                <div class="mr-3 mt-0.5"
                    :class="notification.type === 'success' ? 'text-green-500' : notification.type === 'error' ? 'text-red-500' : 'text-blue-500'">
                    <i :data-lucide="notification.icon" class="h-5 w-5"></i>
                </div>
                <div class="flex-1">
                    <p class="font-medium text-gray-900">{{ notification.title }}</p>
                    <p class="text-sm text-gray-600 mt-1">{{ notification.message }}</p>
                </div>
                <button @click="hideNotification" class="text-gray-400 hover:text-gray-600 ml-2">
                    <i data-lucide="x" class="h-4 w-4"></i>
                </button>
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref, reactive, onMounted } = Vue;

        createApp({
            setup() {
                // 响应式数据
                const sidebarOpen = ref(false);
                const loading = ref(false);
                const selectedUserId = ref('default');
                const authCode = ref('');
                const clientCredentials = ref([]);

                const tokenData = reactive({
                    access_token: '',
                    refresh_token: '',
                    token_type: '',
                    expires_in: '',
                    scope: '',
                    created_at: ''
                });

                const tokenStatus = reactive({
                    accessToken: false,
                    refreshToken: false,
                    isExpired: false,
                    expiresIn: ''
                });

                const notification = reactive({
                    show: false,
                    type: 'info',
                    title: '',
                    message: '',
                    icon: 'info'
                });

                // 方法
                const toggleSidebar = () => {
                    sidebarOpen.value = !sidebarOpen.value;
                };

                const showFeatureComingSoon = (featureName) => {
                    showNotification('info', '功能即将推出', `${featureName}功能正在开发中，敬请期待！`);
                };

                const showNotification = (type, title, message) => {
                    notification.show = true;
                    notification.type = type;
                    notification.title = title;
                    notification.message = message;
                    notification.icon = type === 'success' ? 'check-circle' : type === 'error' ? 'alert-circle' : 'info';

                    setTimeout(() => {
                        hideNotification();
                    }, 5000);
                };

                const hideNotification = () => {
                    notification.show = false;
                };

                const loadClientCredentials = async () => {
                    try {
                        const response = await fetch('/api/external/token/client-credentials');
                        const result = await response.json();

                        if (result.success && result.data) {
                            clientCredentials.value = result.data;
                            // 如果当前选择的用户不在列表中，选择第一个
                            if (clientCredentials.value.length > 0) {
                                const currentUserExists = clientCredentials.value.some(c => c.clientId === selectedUserId.value);
                                if (!currentUserExists) {
                                    selectedUserId.value = clientCredentials.value[0].clientId;
                                }
                            }
                        } else {
                            // 如果获取失败，使用默认的硬编码选项
                            clientCredentials.value = [
                                { clientId: 'default', clientName: '默认用户' },
                                { clientId: 'admin', clientName: '管理员' }
                            ];
                        }
                    } catch (error) {
                        console.error('加载客户端凭据失败:', error);
                        // 如果网络错误，使用默认的硬编码选项
                        clientCredentials.value = [
                            { clientId: 'default', clientName: '默认用户' },
                            { clientId: 'admin', clientName: '管理员' }
                        ];
                    }
                };

                const getTokenByAuthCode = async () => {
                    if (!authCode.value.trim()) {
                        showNotification('error', '输入错误', '请输入有效的授权码');
                        return;
                    }

                    loading.value = true;

                    try {
                        const url = `/api/token/get-token?authCode=${encodeURIComponent(authCode.value)}${selectedUserId.value ? '&userId=' + encodeURIComponent(selectedUserId.value) : ''}`;
                        const response = await fetch(url, { method: 'POST' });
                        const result = await response.json();

                        if (result.success) {
                            // 更新token数据
                            if (result.data) {
                                Object.assign(tokenData, {
                                    access_token: result.data.AccessToken || result.data.access_token,
                                    refresh_token: result.data.RefreshToken || result.data.refresh_token,
                                    token_type: result.data.TokenType || result.data.token_type || 'Bearer',
                                    expires_in: result.data.ExpiresIn || result.data.expires_in,
                                    scope: result.data.Scope || result.data.scope,
                                    created_at: new Date().toISOString(),
                                    user_auth_permanent_code: result.data.UserAuthPermanentCode || result.data.user_auth_permanent_code
                                });
                                updateTokenStatus(tokenData);
                            }

                            showNotification('success', '获取成功', '6天有效期Token获取成功！系统将自动管理Token刷新。');
                            authCode.value = ''; // 清空输入框

                            // 如果返回了permanent code，特别提示用户保存
                            if (result.data && (result.data.UserAuthPermanentCode || result.data.user_auth_permanent_code)) {
                                setTimeout(() => {
                                    showNotification('info', '重要提示', '已获取到永久授权码，建议保存以备后续使用！');
                                }, 3000);
                            }
                        } else {
                            let errorMsg = result.message || '获取Token失败';
                            if (result.error_code === 'INVALID_AUTH_CODE') {
                                errorMsg = '授权码无效或已过期（10分钟有效期），请重新获取授权码';
                            } else if (result.error_code === 'INVALID_APP_KEY') {
                                errorMsg = '应用配置错误，请检查appKey设置';
                            }
                            showNotification('error', '获取失败', errorMsg);
                        }
                    } catch (error) {
                        showNotification('error', '网络错误', '无法连接到服务器，请检查服务是否正常运行');
                    } finally {
                        loading.value = false;
                    }
                };

                const loadTokenStatus = async () => {
                    loading.value = true;

                    try {
                        const response = await fetch(`/api/token/status?userId=${selectedUserId.value}`);
                        const result = await response.json();

                        if (result.success && result.data) {
                            // 检查是否有token
                            if (result.data.has_token && result.data.token_info) {
                                // 从token_info中获取实际的token数据
                                const tokenInfo = result.data.token_info;

                                // 调用valid-token接口获取完整的token数据
                                try {
                                    const tokenResponse = await fetch(`/api/token/info?userId=${selectedUserId.value}`);
                                    const tokenResult = await tokenResponse.json();

                                    if (tokenResult.success && tokenResult.data) {
                                        // 更新tokenData为完整的token信息
                                        Object.assign(tokenData, {
                                            access_token: tokenResult.data.access_token || '',
                                            refresh_token: tokenResult.data.refresh_token || '',
                                            token_type: tokenResult.data.token_type || 'Bearer',
                                            expires_in: tokenResult.data.expires_in || '',
                                            refresh_expires_in: tokenResult.data.refresh_expires_in || '',
                                            scope: tokenResult.data.scope || '',
                                            expires_at: tokenResult.data.expires_at || '',
                                            refresh_expires_at: tokenResult.data.refresh_expires_at || '',
                                            created_at: tokenResult.data.created_at || tokenInfo.created_at || new Date().toISOString(),
                                            user_auth_permanent_code: tokenResult.data.user_auth_permanent_code || tokenData.user_auth_permanent_code || ''
                                        });

                                        updateTokenStatus(tokenData);
                                        showNotification('success', '状态更新', 'Token状态已刷新，包含完整Token信息');
                                    } else {
                                        // 如果获取不到完整token，但status显示有效，则用部分信息更新
                                        updateTokenStatusFromApiResponse(result.data, tokenInfo);
                                        showNotification('success', '状态更新', 'Token状态已刷新');
                                    }
                                } catch (tokenError) {
                                    console.warn('获取完整token信息失败，使用状态信息:', tokenError);
                                    // 降级处理：仅使用status信息更新显示
                                    updateTokenStatusFromApiResponse(result.data, tokenInfo);
                                    showNotification('success', '状态更新', 'Token状态已刷新');
                                }
                            } else {
                                // 没有token
                                resetTokenData();
                                showNotification('info', '状态更新', '当前没有有效的Token');
                            }
                        } else {
                            showNotification('error', '获取失败', result.message || '无法获取Token状态');
                            resetTokenData();
                        }
                    } catch (error) {
                        showNotification('error', '网络错误', '无法连接到服务器');
                        resetTokenData();
                    } finally {
                        loading.value = false;
                    }
                };

                const updateTokenStatusFromApiResponse = (statusData, tokenInfo) => {
                    // 根据status接口的响应更新token状态
                    tokenStatus.accessToken = statusData.has_token && statusData.is_valid;
                    tokenStatus.refreshToken = statusData.has_token; // 假设有token就有refresh token

                    if (tokenInfo) {
                        const expiresAt = new Date(tokenInfo.expires_at).getTime();
                        const now = new Date().getTime();
                        tokenStatus.isExpired = now > expiresAt;

                        if (!tokenStatus.isExpired) {
                            const remainingMinutes = Math.floor((expiresAt - now) / (1000 * 60));
                            const hours = Math.floor(remainingMinutes / 60);
                            const minutes = remainingMinutes % 60;
                            tokenStatus.expiresIn = `${hours}小时${minutes}分钟`;
                        } else {
                            tokenStatus.expiresIn = '已过期';
                        }

                        // 更新基本token信息（即使没有完整token内容）
                        Object.assign(tokenData, {
                            access_token: tokenStatus.accessToken ? '****有效token****' : '',
                            refresh_token: tokenStatus.refreshToken ? '****有效refresh token****' : '',
                            token_type: 'Bearer',
                            expires_in: tokenInfo.time_to_expiry ? Math.floor(tokenInfo.time_to_expiry * 60) : '',
                            scope: 'auth_all',
                            created_at: tokenInfo.created_at || ''
                        });
                    } else {
                        tokenStatus.expiresIn = '未知';
                    }
                };

                const updateTokenStatus = (data) => {
                    tokenStatus.accessToken = !!data.access_token;
                    tokenStatus.refreshToken = !!data.refresh_token;

                    if (data.expires_in && data.created_at) {
                        const expiresAt = new Date(data.created_at).getTime() + (data.expires_in * 1000);
                        const now = new Date().getTime();
                        tokenStatus.isExpired = now > expiresAt;

                        if (!tokenStatus.isExpired) {
                            const remainingSeconds = Math.floor((expiresAt - now) / 1000);
                            const hours = Math.floor(remainingSeconds / 3600);
                            const minutes = Math.floor((remainingSeconds % 3600) / 60);
                            tokenStatus.expiresIn = `${hours}小时${minutes}分钟`;
                        } else {
                            tokenStatus.expiresIn = '已过期';
                        }
                    } else {
                        tokenStatus.expiresIn = '未知';
                    }
                };

                const resetTokenData = () => {
                    Object.assign(tokenData, {
                        access_token: '',
                        refresh_token: '',
                        token_type: '',
                        expires_in: '',
                        scope: '',
                        created_at: ''
                    });

                    Object.assign(tokenStatus, {
                        accessToken: false,
                        refreshToken: false,
                        isExpired: false,
                        expiresIn: ''
                    });
                };

                const refreshToken = async () => {
                    loading.value = true;

                    try {
                        const response = await fetch('/api/token/refresh', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ userId: selectedUserId.value })
                        });

                        const result = await response.json();

                        if (result.success) {
                            Object.assign(tokenData, result.data);
                            updateTokenStatus(result.data);
                            showNotification('success', '刷新成功', 'Token已成功刷新');
                        } else {
                            showNotification('error', '刷新失败', result.message || 'Token刷新失败');
                        }
                    } catch (error) {
                        showNotification('error', '网络错误', '无法连接到服务器');
                    } finally {
                        loading.value = false;
                    }
                };

                const validateToken = async () => {
                    loading.value = true;

                    try {
                        const response = await fetch('/api/token/validate', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ userId: selectedUserId.value })
                        });

                        const result = await response.json();

                        if (result.success) {
                            showNotification('success', '验证成功', 'Token有效且可用');
                        } else {
                            showNotification('error', '验证失败', result.message || 'Token无效或已过期');
                        }
                    } catch (error) {
                        showNotification('error', '网络错误', '无法连接到服务器');
                    } finally {
                        loading.value = false;
                    }
                };

                const revokeToken = async () => {
                    if (!confirm('确定要撤销Token吗？这将使Token立即失效。')) {
                        return;
                    }

                    loading.value = true;

                    try {
                        const response = await fetch('/api/token/revoke', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ userId: selectedUserId.value })
                        });

                        const result = await response.json();

                        if (result.success) {
                            resetTokenData();
                            showNotification('success', '撤销成功', 'Token已被撤销');
                        } else {
                            showNotification('error', '撤销失败', result.message || 'Token撤销失败');
                        }
                    } catch (error) {
                        showNotification('error', '网络错误', '无法连接到服务器');
                    } finally {
                        loading.value = false;
                    }
                };

                const copyTokenToClipboard = async () => {
                    try {
                        await navigator.clipboard.writeText(tokenData.access_token);
                        showNotification('success', '复制成功', 'Token已复制到剪贴板');
                    } catch (error) {
                        showNotification('error', '复制失败', '无法复制到剪贴板');
                    }
                };

                const copyPermanentCode = async () => {
                    try {
                        await navigator.clipboard.writeText(tokenData.user_auth_permanent_code);
                        showNotification('success', '复制成功', '永久授权码已复制到剪贴板');
                    } catch (error) {
                        showNotification('error', '复制失败', '无法复制到剪贴板');
                    }
                };

                const copyAuthUrl = async () => {
                    const authUrl = 'https://market.chanjet.com/user/v2/authorize?appkey=YOUR_APP_KEY&redirect_uri=YOUR_REDIRECT_URI&scope=auth_all&state=test123';
                    try {
                        await navigator.clipboard.writeText(authUrl);
                        showNotification('success', '复制成功', '授权地址已复制，请替换YOUR_APP_KEY等参数');
                    } catch (error) {
                        showNotification('error', '复制失败', '无法复制到剪贴板');
                    }
                };

                const exportToken = () => {
                    const data = JSON.stringify(tokenData, null, 2);
                    const blob = new Blob([data], { type: 'application/json' });
                    const url = URL.createObjectURL(blob);

                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `token_${selectedUserId.value}_${new Date().toISOString().split('T')[0]}.json`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);

                    showNotification('success', '导出成功', 'Token数据已导出');
                };

                const clearToken = () => {
                    if (confirm('确定要清空Token缓存吗？')) {
                        resetTokenData();
                        showNotification('info', '清空完成', 'Token缓存已清空');
                    }
                };

                // 生命周期
                onMounted(async () => {
                    // 初始化Lucide图标
                    lucide.createIcons();

                    // 先加载客户端凭据
                    await loadClientCredentials();

                    // 然后加载Token状态
                    loadTokenStatus();
                });

                return {
                    sidebarOpen,
                    loading,
                    selectedUserId,
                    authCode,
                    clientCredentials,
                    tokenData,
                    tokenStatus,
                    notification,
                    toggleSidebar,
                    showFeatureComingSoon,
                    hideNotification,
                    loadClientCredentials,
                    getTokenByAuthCode,
                    loadTokenStatus,
                    updateTokenStatusFromApiResponse,
                    refreshToken,
                    validateToken,
                    revokeToken,
                    copyTokenToClipboard,
                    copyPermanentCode,
                    copyAuthUrl,
                    exportToken,
                    clearToken
                };
            }
        }).mount('#app');
    </script>
</body>

</html>