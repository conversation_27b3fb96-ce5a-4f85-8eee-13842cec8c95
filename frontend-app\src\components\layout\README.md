# 布局系统

本项目采用分层布局系统，提供灵活的页面布局支持。

## 布局层次结构

```
BaseLayout (基础模板)
├── AdminLayout (管理后台布局)
├── SimpleLayout (简单页面布局) 
└── BlankLayout (空白布局)
```

## 布局组件

### BaseLayout (基础布局模板)
- **用途**: 所有布局的基础模板，提供插槽和基础样式
- **特性**: 
  - 支持侧边栏开关
  - 支持头部开关  
  - 可自定义样式类
  - 内置加载状态处理

### AdminLayout (管理后台布局)
- **用途**: 管理后台页面的标准布局
- **特性**:
  - 包含完整的侧边栏导航
  - 包含系统状态头部
  - 面包屑导航
  - 系统状态监控

### SimpleLayout (简单布局)
- **用途**: 简单页面，如详情页、表单页等
- **特性**:
  - 只有头部，无侧边栏
  - 可选返回按钮
  - 可设置页面标题

### BlankLayout (空白布局)  
- **用途**: 登录页、404页面等
- **特性**:
  - 无头部、无侧边栏
  - 居中内容区域

## 使用示例

### 管理后台页面
```vue
<template>
  <AdminLayout>
    <!-- 你的页面内容 -->
  </AdminLayout>
</template>

<script setup>
import { AdminLayout } from '@/components/layout'
</script>
```

### 简单页面
```vue
<template>
  <SimpleLayout title="用户详情" :show-back="true">
    <!-- 你的页面内容 -->
  </SimpleLayout>
</template>

<script setup>
import { SimpleLayout } from '@/components/layout'
</script>
```

### 空白页面
```vue
<template>
  <BlankLayout>
    <!-- 登录表单等 -->
  </BlankLayout>
</template>

<script setup>
import { BlankLayout } from '@/components/layout'
</script>
```

### 自定义布局
```vue
<template>
  <BaseLayout 
    :show-sidebar="false" 
    main-class="custom-main-style"
  >
    <template #header>
      <!-- 自定义头部 -->
    </template>
    
    <!-- 页面内容 -->
  </BaseLayout>
</template>

<script setup>
import { BaseLayout } from '@/components/layout'
</script>
```

## 扩展布局

如需新的布局变体，继承BaseLayout即可：

```vue
<!-- MyCustomLayout.vue -->
<template>
  <BaseLayout :show-sidebar="false" main-class="my-custom-class">
    <template #header>
      <!-- 自定义头部逻辑 -->
    </template>
    
    <slot />
  </BaseLayout>
</template>
```

## 设计原则

1. **分离关注点**: BaseLayout只管布局结构，具体布局包含业务逻辑
2. **插槽优先**: 使用插槽提供最大灵活性
3. **可配置性**: 通过props控制布局行为
4. **可复用性**: 布局组件可在不同页面间复用 